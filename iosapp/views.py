from django.contrib.auth import authenticate, login
from django.contrib.auth.models import User
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt, ensure_csrf_cookie
from django.middleware.csrf import get_token
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated, AllowAny
from rest_framework.response import Response
from rest_framework.pagination import PageNumberPagination
from django.db.models import Count, Avg, Q, Max
from django.utils import timezone
from datetime import datetime, timedelta
from .models import IOSDevice
from .serializers import (
    IOSDeviceSerializer,
    IOSBlendComponentSerializer,
    IOSBeanOccurrenceSerializer,
    IOSBrewingRecordSerializer,
    IOSEquipmentSerializer,
    IOSCoffeeBeanSerializer
)
from my.models import BrewingRecord, Equipment, CoffeeBean, HindsightStats, BeanOccurrence, FlavorTag, BlendComponent
import logging
from django.http import HttpResponse
from django.utils import timezone
from django.utils.dateparse import parse_date
from django.utils.dateformat import format
from django.utils.translation import gettext as _
from django.core.exceptions import ValidationError
from rest_framework import status
from django.conf import settings
from django.db import models
from rest_framework_simplejwt.tokens import RefreshToken, AccessToken
from rest_framework_simplejwt.views import TokenRefreshView
from rest_framework_simplejwt.exceptions import TokenError, InvalidToken
from rest_framework_simplejwt.settings import api_settings
import json
from .cache_utils import ios_cache_response, invalidate_ios_cache
import time
from django.shortcuts import render
import uuid
from django.core.cache import cache
from decimal import Decimal
import decimal
from rest_framework import serializers
import re
import time

def get_client_ip(request):
    """获取客户端真实IP地址"""
    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        ip = x_forwarded_for.split(',')[0].strip()
    else:
        ip = request.META.get('REMOTE_ADDR')
    return ip

def validate_username_format(username):
    """
    验证用户名格式

    规则：
    - 长度5-20个字符
    - 只能包含字母、数字
    - 必须以字母开头
    - 不能在黑名单中
    """
    if len(username) < 5:
        return {'valid': False, 'message': '用户名至少需要5个字符'}

    if len(username) > 20:
        return {'valid': False, 'message': '用户名不能超过20个字符'}

    # 检查是否只包含字母和数字
    if not re.match(r'^[a-zA-Z][a-zA-Z0-9]*$', username):
        return {'valid': False, 'message': '用户名只能包含字母和数字，且必须以字母开头'}

    # 检查是否在黑名单中
    from django.conf import settings
    blacklist = getattr(settings, 'ACCOUNT_USERNAME_BLACKLIST', [])
    if username.lower() in [name.lower() for name in blacklist]:
        return {'valid': False, 'message': '该用户名不可用'}

    return {'valid': True, 'message': '用户名格式正确'}

def generate_username_suggestions(username):
    """
    生成用户名建议

    基于原用户名生成3-5个可能的替代方案
    """
    suggestions = []
    base_username = re.sub(r'[^a-zA-Z]', '', username)[:10]  # 只保留字母，最多10个字符

    if len(base_username) < 3:
        base_username = 'brew'  # 默认前缀

    # 生成数字后缀建议
    import random
    for i in range(3):
        suffix = random.randint(100, 9999)
        suggestion = f"{base_username}{suffix}"
        if len(suggestion) >= 5 and len(suggestion) <= 20:
            suggestions.append(suggestion)

    # 生成咖啡相关后缀建议
    coffee_suffixes = ['brew', 'roast', 'bean', 'shot', 'pour']
    for suffix in coffee_suffixes[:2]:
        suggestion = f"{base_username}{suffix}"
        if len(suggestion) >= 5 and len(suggestion) <= 20:
            suggestions.append(suggestion)

    return suggestions[:5]  # 最多返回5个建议

def generate_distinct_colors(n):
    """生成n个有区分度的颜色"""
    colors = []
    for i in range(n):
        hue = i * (360 / n)  # 均匀分布的色相
        # 生成两种颜色：实心(图例用)和半透明(日历事件用)
        base_color = f"hsl({hue} 70% 50%)"
        colors.append({
            'solid': base_color,  # 图例用
            'transparent': f"color-mix(in oklab, {base_color} 50%, transparent)"  # 日历事件用
        })
    return colors

"""
iOS应用视图模块

本模块提供iOS应用客户端所需的API视图。
视图主要负责：
1. 处理HTTP请求响应
2. 处理权限验证
3. 调用模型层业务逻辑
4. 序列化响应数据

此模块的业务逻辑已迁移至models.py，视图层主要负责协调而非实现业务规则。
"""

logger = logging.getLogger(__name__)

# 设置令牌有效期
TOKEN_LIFETIME = getattr(settings, 'SIMPLE_JWT', {}).get('ACCESS_TOKEN_LIFETIME', timedelta(minutes=30))
REFRESH_TOKEN_LIFETIME = getattr(settings, 'SIMPLE_JWT', {}).get('REFRESH_TOKEN_LIFETIME', timedelta(days=7))

# 帮助函数用于确保对象可序列化
def ensure_serializable(obj):
    """确保对象可以通过JSON序列化"""
    if isinstance(obj, dict):
        return {k: ensure_serializable(v) for k, v in obj.items()}
    elif isinstance(obj, (list, tuple)):
        return [ensure_serializable(item) for item in obj]
    elif isinstance(obj, (int, float, str, bool, type(None))):
        return obj
    else:
        # 将其他类型转换为字符串
        return str(obj)

@api_view(['GET'])
@permission_classes([AllowAny])
def api_test(request):
    """测试API是否正常工作"""
    return Response({
        'status': 'ok',
        'message': 'API is working',
        'request_path': request.path,
        'request_method': request.method,
        'request_headers': dict(request.headers),
    })

@ensure_csrf_cookie
@api_view(['GET'])
@permission_classes([AllowAny])
def get_csrf_token(request):
    """获取CSRF Token的API"""
    logger.info("CSRF Token请求 - 路径: %s", request.path)

    # 获取CSRF令牌
    token = get_token(request)

    # 记录令牌信息（部分隐藏）
    if token:
        token_preview = token[:10] + '...' if len(token) > 10 else token
        logger.info(f"生成CSRF令牌: {token_preview}")

        # 设置响应对象
        response = Response({'csrfToken': token})

        # 确保cookie正确设置
        response.set_cookie(
            'csrftoken',
            token,
            max_age=3600 * 24 * 7,  # 7天有效期
            domain=None,
            secure=request.is_secure(),
            httponly=False,  # 允许JavaScript访问
            samesite='Lax'
        )

        # 同时在响应头中设置令牌，以便客户端可以直接获取
        response['X-CSRFToken'] = token

        return response
    else:
        logger.warning("无法生成CSRF令牌")
        return Response({'error': '无法生成CSRF令牌'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@csrf_exempt
@api_view(['POST'])
@permission_classes([AllowAny])
def api_login(request):
    """iOS客户端登录API"""
    logger.info("iOS登录请求 - 路径: %s", request.path)

    # 获取请求参数
    username = request.data.get('username')
    password = request.data.get('password')
    device_id = request.data.get('device_id')

    # 设备信息
    device_data = {
        'device_model': request.data.get('device_model'),
        'device_os_version': request.data.get('device_os_version'),
        'app_version': request.data.get('app_version')
    }

    logger.info(f"iOS登录尝试 - 用户名: {username}, 设备ID: {device_id}, 设备型号: {device_data.get('device_model')}")

    if not username or not password:
        logger.warning("iOS登录请求缺少用户名或密码")
        return Response({
            'error': '请提供用户名和密码'
        }, status=status.HTTP_400_BAD_REQUEST)

    if not device_id:
        logger.warning("iOS登录请求缺少设备ID")
        return Response({
            'error': '请提供设备ID'
        }, status=status.HTTP_400_BAD_REQUEST)

    try:
        # 检查设备是否在黑名单中
        if IOSDevice.objects.is_blacklisted(device_id):
            logger.warning(f"设备在黑名单中 - 设备ID: {device_id}")
            return Response({
                'error': '此设备已被禁止登录，请联系管理员'
            }, status=status.HTTP_403_FORBIDDEN)

        # 验证用户
        user = authenticate(request, username=username, password=password)
        if user is None:
            logger.warning(f"iOS登录失败 - 用户名或密码错误: {username}")
            return Response({
                'error': '用户名或密码错误'
            }, status=status.HTTP_401_UNAUTHORIZED)

        if not user.is_active:
            logger.warning(f"iOS登录失败 - 账号已被禁用: {username}")
            return Response({
                'error': '账号已被禁用'
            }, status=status.HTTP_403_FORBIDDEN)

        login(request, user)
        logger.info(f"iOS用户登录成功: {user.username} (ID: {user.id})")

        # 注册或更新设备信息
        device, created = IOSDevice.objects.register_or_update_device(user, device_id, device_data)
        if device is None:
            logger.error("设备注册失败，但继续登录流程")
        elif created:
            logger.info(f"新iOS设备注册成功 - 设备ID: {device_id}, 用户: {user.username}")
        else:
            logger.info(f"已有iOS设备更新成功 - 设备ID: {device_id}, 用户: {user.username}")

        # 检查用户的设备数量并记录
        device_count = IOSDevice.objects.filter(user=user).count()
        logger.info(f"用户 {user.username} 当前关联的iOS设备数量: {device_count}")

        # 生成令牌
        token_data = IOSDevice.generate_tokens_for_user(user, device_id)

        # 构建响应数据
        response_data = {
            'id': user.id,
            'username': user.username,
            'email': user.email,
            'first_name': user.first_name,
            **token_data
        }

        return Response(response_data)

    except Exception as e:
        logger.error(f"iOS登录处理时发生异常: {str(e)}", exc_info=True)
        return Response({
            'error': '服务器错误，请稍后重试'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['POST'])
@permission_classes([AllowAny])
@csrf_exempt
def api_register(request):
    """
    iOS用户注册API
    """
    logger.info("处理iOS注册请求")

    try:
        data = request.data
        username = data.get('username', '').strip()
        email = data.get('email', '').strip()
        password = data.get('password', '').strip()
        # 移除first_name字段的处理

        # 获取设备信息
        device_id = data.get('device_id')
        device_model = data.get('device_model', '')
        device_os_version = data.get('device_os_version', '')
        app_version = data.get('app_version', '')

        # 验证必需字段
        if not username or not email or not password:
            return Response({
                'error': '用户名、邮箱和密码不能为空'
            }, status=status.HTTP_400_BAD_REQUEST)

        if not device_id:
            logger.warning("iOS注册请求缺少设备ID")
            return Response({
                'error': '请提供设备ID'
            }, status=status.HTTP_400_BAD_REQUEST)

        # 检查用户名是否已存在
        if User.objects.filter(username=username).exists():
            return Response({
                'error': '用户名已存在'
            }, status=status.HTTP_400_BAD_REQUEST)

        # 检查邮箱是否已存在
        if User.objects.filter(email=email).exists():
            return Response({
                'error': '邮箱已被注册'
            }, status=status.HTTP_400_BAD_REQUEST)

        # 检查设备是否在黑名单中
        if IOSDevice.objects.is_blacklisted(device_id):
            logger.warning(f"设备在黑名单中 - 设备ID: {device_id}")
            return Response({
                'error': '此设备已被禁止注册，请联系管理员'
            }, status=status.HTTP_403_FORBIDDEN)

        # 创建用户 - 不再设置first_name
        user = User.objects.create_user(
            username=username,
            email=email,
            password=password,
            first_name=''  # 昵称留空，用户可以在注册后再设置
        )

        logger.info(f"iOS注册成功 - 用户: {username}, 邮箱: {email}")

        # 发送邮件验证
        try:
            from allauth.account.utils import send_email_confirmation
            send_email_confirmation(request, user, signup=True)
            logger.info(f"邮件验证已发送 - 用户: {username}, 邮箱: {email}")
        except Exception as e:
            logger.error(f"发送邮件验证失败 - 用户: {username}, 错误: {str(e)}")
            # 邮件发送失败不影响注册流程

        # 注册设备
        device_data = {
            'device_model': device_model,
            'device_os_version': device_os_version,
            'app_version': app_version
        }

        device, created = IOSDevice.objects.register_or_update_device(user, device_id, device_data)
        if device is None:
            logger.error("设备注册失败，但继续注册流程")
        elif created:
            logger.info(f"新iOS设备注册成功 - 设备ID: {device_id}, 用户: {user.username}")
        else:
            logger.info(f"已有iOS设备更新成功 - 设备ID: {device_id}, 用户: {user.username}")

        # 生成令牌
        token_data = IOSDevice.generate_tokens_for_user(user, device_id)

        # 构建响应数据
        response_data = {
            'id': user.id,
            'username': user.username,
            'email': user.email,
            'first_name': user.first_name,  # 仍然返回first_name，但此时为空
            'message': '注册成功',
            **token_data
        }

        return Response(response_data, status=status.HTTP_201_CREATED)

    except Exception as e:
        logger.error(f"iOS注册处理时发生异常: {str(e)}", exc_info=True)
        return Response({
            'error': '服务器错误，请稍后重试'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['POST'])
@permission_classes([AllowAny])
@csrf_exempt
def api_password_reset(request):
    """
    iOS密码重置API
    """
    logger.info("处理iOS密码重置请求")

    try:
        data = request.data
        email = data.get('email', '').strip()

        # 验证邮箱
        if not email:
            return Response({
                'error': '邮箱不能为空'
            }, status=status.HTTP_400_BAD_REQUEST)

        # 使用 allauth 的密码重置功能
        from allauth.account.forms import ResetPasswordForm
        from allauth.account.utils import filter_users_by_email

        # 检查用户是否存在
        users = filter_users_by_email(email)
        if not users:
            # 为了安全考虑，不透露用户是否存在，但仍然返回成功消息
            logger.info(f"密码重置请求 - 邮箱不存在: {email}")
            return Response({
                'success': True,
                'message': '如果该邮箱已注册，您将收到密码重置邮件'
            })

        # 创建重置密码表单并发送邮件
        form = ResetPasswordForm({'email': email})
        if form.is_valid():
            # 设置请求对象，allauth 需要它来生成重置链接
            form.request = request
            form.save(request)

            logger.info(f"密码重置邮件已发送 - 邮箱: {email}")
            return Response({
                'success': True,
                'message': '密码重置邮件已发送，请检查您的邮箱'
            })
        else:
            logger.error(f"密码重置表单验证失败 - 邮箱: {email}, 错误: {form.errors}")
            return Response({
                'error': '邮箱格式不正确'
            }, status=status.HTTP_400_BAD_REQUEST)

    except Exception as e:
        logger.error(f"iOS密码重置处理时发生异常: {str(e)}", exc_info=True)
        return Response({
            'error': '服务器错误，请稍后重试'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
@permission_classes([AllowAny])
@csrf_exempt
def check_username_availability(request):
    """
    iOS用户名可用性检查API

    支持高效的用户名可用性检测，包含：
    - Redis缓存机制（5分钟TTL）
    - IP速率限制（30次/分钟）
    - PostgreSQL唯一索引验证
    - 用户名格式验证
    """
    logger.info("处理iOS用户名可用性检查请求")

    try:
        # 获取客户端IP地址
        client_ip = get_client_ip(request)

        # 实现分层速率限制策略
        # 第一层：每分钟最多10次请求
        rate_limit_key_minute = f"username_check_rate_minute:{client_ip}"
        current_requests_minute = cache.get(rate_limit_key_minute, 0)

        # 第二层：每小时最多60次请求
        rate_limit_key_hour = f"username_check_rate_hour:{client_ip}"
        current_requests_hour = cache.get(rate_limit_key_hour, 0)

        if current_requests_minute >= 10:
            logger.warning(f"用户名检查请求超过每分钟速率限制 - IP: {client_ip}")
            return Response({
                'available': False,
                'message': '请求过于频繁，请等待1分钟后再试',
                'suggestions': [],
                'retry_after': 60
            }, status=status.HTTP_429_TOO_MANY_REQUESTS)

        if current_requests_hour >= 60:
            logger.warning(f"用户名检查请求超过每小时速率限制 - IP: {client_ip}")
            return Response({
                'available': False,
                'message': '请求过于频繁，请等待1小时后再试',
                'suggestions': [],
                'retry_after': 3600
            }, status=status.HTTP_429_TOO_MANY_REQUESTS)

        # 增加请求计数
        cache.set(rate_limit_key_minute, current_requests_minute + 1, timeout=60)
        cache.set(rate_limit_key_hour, current_requests_hour + 1, timeout=3600)

        # 获取用户名参数
        username = request.query_params.get('username', '').strip()

        if not username:
            return Response({
                'available': False,
                'message': '用户名不能为空',
                'suggestions': []
            }, status=status.HTTP_400_BAD_REQUEST)

        # 验证用户名格式
        validation_result = validate_username_format(username)
        if not validation_result['valid']:
            return Response({
                'available': False,
                'message': validation_result['message'],
                'suggestions': []
            }, status=status.HTTP_400_BAD_REQUEST)

        # 检查Redis缓存
        cache_key = f"username_available:{username.lower()}"
        cached_result = cache.get(cache_key)

        if cached_result is not None:
            logger.info(f"用户名可用性检查命中缓存 - 用户名: {username}")
            return Response(cached_result)

        # 检查数据库中是否存在该用户名
        username_exists = User.objects.filter(username__iexact=username).exists()

        # 构建响应数据
        if username_exists:
            response_data = {
                'available': False,
                'message': '用户名已被占用',
                'suggestions': generate_username_suggestions(username)
            }
        else:
            response_data = {
                'available': True,
                'message': '用户名可用',
                'suggestions': []
            }

        # 缓存结果（5分钟TTL）
        cache.set(cache_key, response_data, timeout=300)

        logger.info(f"用户名可用性检查完成 - 用户名: {username}, 可用: {response_data['available']}")
        return Response(response_data)

    except Exception as e:
        logger.error(f"iOS用户名可用性检查处理时发生异常: {str(e)}", exc_info=True)
        return Response({
            'available': False,
            'message': '服务器错误，请稍后重试',
            'suggestions': []
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
@ios_cache_response('equipment_list')
def equipment_list(request):
    """获取设备列表"""
    logger.info("开始处理设备列表请求")
    try:
        # 获取分页参数
        page_size = int(request.query_params.get('page_size', 20))
        logger.debug(f"分页大小: {page_size}")

        # 检查是否强制刷新
        force_refresh = request.query_params.get('force_refresh', 'false').lower() == 'true'
        if force_refresh:
            logger.info("强制刷新设备列表")
            invalidate_ios_cache('equipment_list', request.user.id)

        # 获取设备，只过滤掉已删除的设备，包含已归档的设备
        equipment = Equipment.objects.filter(
            user=request.user,
            is_deleted=False
        ).select_related(
            'user'
        ).order_by('-created_at')

        # 确保设备使用统计被预先加载
        if force_refresh:
            for equip in equipment:
                # 触发缓存预热
                _ = equip.get_usage_count()
                _ = equip.get_last_used_datetime()

        # 使用分页器
        paginator = PageNumberPagination()
        paginator.page_size = page_size
        page = paginator.paginate_queryset(equipment, request)

        if page is not None:
            serializer = IOSEquipmentSerializer(page, many=True)
            data = serializer.data
            # 过滤掉任何None值的记录
            data = [item for item in data if item is not None]
            logger.info(f"成功获取设备列表，共 {len(data)} 条记录")

            # 获取最后更新时间戳
            latest_equipment = Equipment.objects.filter(
                user=request.user,
                is_deleted=False
            ).order_by('-created_at').first()

            data_version = 0
            if latest_equipment:
                # 使用created_at或updated_at字段中较新的时间作为版本号
                if hasattr(latest_equipment, 'updated_at'):
                    data_version = int(latest_equipment.updated_at.timestamp())
                else:
                    data_version = int(latest_equipment.created_at.timestamp())

            response_data = {
                'count': equipment.count(),
                'next': paginator.get_next_link(),
                'previous': paginator.get_previous_link(),
                'results': data,
                'data_version': data_version
            }
            return Response(response_data)

        return Response({'error': '分页错误'}, status=status.HTTP_400_BAD_REQUEST)

    except Exception as e:
        logger.error(f"处理设备列表请求时发生错误: {str(e)}", exc_info=True)
        return Response(
            {'error': '获取设备列表失败'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['GET'])
@permission_classes([IsAuthenticated])
@ios_cache_response('bean_list', timeout=300)  # 5分钟缓存
def bean_list(request):
    """获取咖啡豆列表"""
    logger.info("开始处理咖啡豆列表请求")
    try:
        # 获取分页参数
        page_size = int(request.query_params.get('page_size', 20))
        logger.debug(f"分页大小: {page_size}")

        # 获取咖啡豆，只过滤掉已删除的咖啡豆，但包含已归档的
        beans = CoffeeBean.objects.filter(
            user=request.user,
            is_deleted=False
        ).select_related(
            'user'
        ).prefetch_related(
            'flavor_tags'
        ).order_by('-created_at')

        total_count = beans.count()
        logger.debug(f"总记录数: {total_count}")

        # 使用分页器
        paginator = PageNumberPagination()
        paginator.page_size = page_size
        page = paginator.paginate_queryset(beans, request)

        if page is not None:
            serializer = IOSCoffeeBeanSerializer(page, many=True)
            data = serializer.data
            # 过滤掉任何None值的记录
            data = [bean for bean in data if bean is not None]
            logger.info(f"成功获取咖啡豆列表，共 {len(data)} 条记录")

            # 构建标准的分页响应
            response_data = {
                'count': total_count,
                'next': paginator.get_next_link(),
                'previous': paginator.get_previous_link(),
                'results': data
            }

            # 记录响应数据的结构
            logger.debug(f"响应数据结构: {response_data.keys()}")
            logger.debug(f"第一条记录的结构: {data[0].keys() if data else None}")

            return Response(response_data)

        return Response({'error': '分页错误'}, status=status.HTTP_400_BAD_REQUEST)

    except Exception as e:
        logger.error(f"处理咖啡豆列表请求时发生错误: {str(e)}", exc_info=True)
        return Response(
            {'error': '获取咖啡豆列表失败'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['GET'])
@permission_classes([IsAuthenticated])
@ios_cache_response('brewlog_list')
def brewlog_list(request):
    """获取冲煮记录列表"""
    logger.info("开始处理冲煮记录列表请求")
    try:
        # 获取分页参数
        page_size = int(request.query_params.get('page_size', 20))
        logger.debug(f"分页大小: {page_size}")

        # 获取用户的冲煮记录
        records = BrewingRecord.objects.filter(
            user=request.user
        ).select_related(
            'brewing_equipment',
            'coffee_bean',
            'gadget_kit'
        ).prefetch_related(
            'flavor_tags',
            'gadgets'
        ).order_by('-created_at')

        # 使用分页器
        paginator = PageNumberPagination()
        paginator.page_size = page_size
        page = paginator.paginate_queryset(records, request)

        if page is not None:
            serializer = IOSBrewingRecordSerializer(page, many=True)
            data = serializer.data
            # 过滤掉任何None值的记录
            data = [record for record in data if record is not None]

            # 检查每条记录的coffee_bean字段是否有id
            for record in data:
                if 'coffee_bean' not in record or not isinstance(record['coffee_bean'], dict) or 'id' not in record['coffee_bean']:
                    # 如果没有id，添加默认的coffee_bean对象
                    record['coffee_bean'] = {
                        'id': 0,
                        'name': '',
                        'roaster': '',
                        'origin': '',
                        'process': '',
                        'type': 'SINGLE',
                        'type_display': '单品',
                        'roast_level': 3,
                        'roast_level_display': '中浅烘',
                        'description': '',
                        'price': None,
                        'weight': None,
                        'purchase_date': None,
                        'roast_date': None,
                        'is_finished': False,
                        'created_at': None,
                        'is_favorite': False,
                        'is_decaf': False,
                        'is_archived': False,
                        'is_deleted': False,
                        'altitude_type': 'SINGLE',
                        'altitude_single': None,
                        'altitude_min': None,
                        'altitude_max': None,
                        'region': '',
                        'finca': '',
                        'variety': '',
                        'barcode': '',
                        'rest_period_min': None,
                        'rest_period_max': None,
                        'rest_period_progress': None,
                        'stock_status': '充足',
                        'deleted_at': None,
                        'bag_remain': None,
                        'notes': '',
                        'taste_notes': []
                    }

            logger.info(f"成功获取冲煮记录列表，共 {len(data)} 条记录")
            response_data = {
                'count': records.count(),
                'next': paginator.get_next_link(),
                'previous': paginator.get_previous_link(),
                'results': data
            }
            return Response(response_data)

        return Response({'error': '分页错误'}, status=status.HTTP_400_BAD_REQUEST)

    except Exception as e:
        logger.error(f"处理冲煮记录列表请求时发生错误: {str(e)}", exc_info=True)
        return Response(
            {'error': '获取冲煮记录列表失败'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['GET'])
@permission_classes([IsAuthenticated])
@ios_cache_response('brewlog_statistics')
def brewlog_statistics(request):
    """获取冲煮记录统计信息"""
    try:
        # 使用BrewingRecord类方法而不是manager方法
        stats = BrewingRecord.get_brewing_stats(request.user)
        return Response(stats)
    except Exception as e:
        logger.error(f"获取冲煮记录统计时发生错误: {str(e)}", exc_info=True)
        return Response(
            {'error': '获取统计信息失败'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['GET'])
@permission_classes([IsAuthenticated])
@ios_cache_response('brew_methods_list', timeout=604800)  # 1周缓存
def brew_methods_list(request):
    """获取冲煮方法列表"""
    try:
        methods = Equipment.BREW_METHODS
        return Response([
            {
                'id': str(i + 1),
                'value': method[0],
                'name': method[1]
            }
            for i, method in enumerate(methods)
        ])
    except Exception as e:
        logger.error(f"获取冲煮方法列表时发生错误: {str(e)}", exc_info=True)
        return Response(
            {'error': '获取冲煮方法列表失败'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['GET'])
@permission_classes([IsAuthenticated])
@ios_cache_response('hindsight_data')
def hindsight_data(request):
    """获取指定年份或时间范围的冲煮记录统计数据"""
    try:
        # 获取参数
        time_range = request.query_params.get('time_range')
        year = request.query_params.get('year')

        # 记录请求参数
        logger.info("获取后见之明数据 - 参数: year=%s, time_range=%s", year, time_range)

        # 根据参数类型初始化统计对象
        if year:
            year = int(year)
            logger.info("使用年份模式: %s", year)
            stats = HindsightStats(user=request.user, year=year)
        elif time_range:
            logger.info("使用时间范围模式: %s", time_range)
            # 检查time_range参数是否有效
            if time_range not in HindsightStats.TIME_RANGES:
                logger.warning("无效的time_range参数: %s", time_range)
                return Response(
                    {'error': f'无效的时间范围参数: {time_range}'},
                    status=status.HTTP_400_BAD_REQUEST
                )
            stats = HindsightStats(user=request.user, time_range=time_range)
        else:
            # 默认使用当前年份
            current_year = timezone.now().year
            logger.info("未提供参数，使用默认年份: %s", current_year)
            stats = HindsightStats(user=request.user, year=current_year)

        # 计算统计数据
        stats_data = stats.calculate_all_stats()

        # 确保响应中包含必需字段的默认空列表
        if 'equipment_stats' not in stats_data:
            stats_data['equipment_stats'] = []
        if 'bean_stats' not in stats_data:
            stats_data['bean_stats'] = []
        if 'monthly_brews' not in stats_data:
            stats_data['monthly_brews'] = []
        if 'rating_distribution' not in stats_data:
            stats_data['rating_distribution'] = []

        # 添加time_range字段到响应中，以便客户端知道使用了哪种模式
        if time_range:
            stats_data['time_range'] = time_range

        logger.info("成功生成后见之明数据，记录数: %s", stats_data.get('total_records', 0))
        return Response(stats_data)

    except Exception as e:
        logger.error("处理后见之明数据时出错: %s", str(e), exc_info=True)
        return Response(
            {'error': f'处理数据时出错: {str(e)}'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['GET'])
@permission_classes([IsAuthenticated])
@ios_cache_response('heatmap_data')
def heatmap_data(request):
    """获取指定年份的热力图数据"""
    try:
        year = int(request.query_params.get('year', timezone.now().year))
        logger.info("获取热力图数据 - 年份: %s", year)

        # 使用BrewingRecord.objects来调用manager方法
        calendar_data = BrewingRecord.objects.generate_calendar_data(request.user, year)

        # 转换为iOS客户端期望的格式
        data = {}
        total_brews = 0
        active_days = 0

        # 处理日历数据，转换为{日期: 冲煮次数}格式
        for item in calendar_data.get('calendar_data', []):
            if item['count'] > 0:
                data[item['date']] = item['count']
                total_brews += item['count']
                active_days += 1

        # 计算平均每天冲煮次数
        average_brews_per_day = round(total_brews / active_days, 2) if active_days > 0 else 0.0

        # 构建iOS客户端期望的响应格式
        response_data = {
            'data': data,
            'total_brews': total_brews,
            'active_days': active_days,
            'average_brews_per_day': average_brews_per_day
        }

        return Response(response_data)

    except Exception as e:
        logger.error("获取热力图数据错误: %s", str(e), exc_info=True)
        return Response(
            {'error': str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['GET'])
@permission_classes([IsAuthenticated])
@ios_cache_response('bean_calendar_data', timeout=300)  # 5分钟缓存
def bean_calendar_data(request):
    """获取指定年月的咖啡豆日历数据"""
    try:
        year = int(request.query_params.get('year', timezone.now().year))
        month = int(request.query_params.get('month', timezone.now().month))
        logger.info("获取咖啡豆日历数据 - 年份: %s, 月份: %s", year, month)

        from datetime import date, timedelta
        import calendar

        # 计算日历需要显示的日期范围
        current_date = date(year, month, 1)
        first_day = current_date.replace(day=1)
        last_day = (first_day + timedelta(days=32)).replace(day=1) - timedelta(days=1)

        # 计算日历起始日期（包括上月残余日期）
        calendar_start = first_day - timedelta(days=first_day.weekday())
        # 计算日历结束日期（包括下月残余日期）
        calendar_end = last_day + timedelta(days=(6 - last_day.weekday()))

        # 获取活跃的咖啡豆
        active_beans = CoffeeBean.get_calendar_beans(request.user, calendar_start, calendar_end)

        # 为每个咖啡豆生成唯一颜色
        colors = generate_distinct_colors(len(active_beans))
        beans_with_colors = []
        bean_colors = {}  # 用于在事件中查找颜色

        for bean, color in zip(active_beans, colors):
            bean_info = bean.get_calendar_display_info(color=color)
            beans_with_colors.append(bean_info)
            bean_colors[bean.id] = color

        # 构建日历数据
        daily_events = {}
        current = calendar_start
        while current <= calendar_end:
            date_str = current.strftime('%Y-%m-%d')
            daily_events[date_str] = {
                'purchase_events': [],
                'roast_events': [],
                'rest_events': []
            }

            for bean in active_beans:
                color = bean_colors[bean.id]

                # 购买日期
                if bean.created_at.date() == current:
                    daily_events[date_str]['purchase_events'].append(
                        bean.get_calendar_display_info(color=color)
                    )

                # 烘焙日期
                if bean.roast_date and bean.roast_date == current:
                    daily_events[date_str]['roast_events'].append(
                        bean.get_calendar_display_info(color=color)
                    )

                # 最佳赏味期
                rest_start, rest_end = bean.get_rest_period_dates()
                if rest_start and rest_end and rest_start <= current <= rest_end:
                    daily_events[date_str]['rest_events'].append(
                        bean.get_calendar_display_info(color=color)
                    )

            current += timedelta(days=1)

        _, days_in_month = calendar.monthrange(year, month)

        return Response({
            'daily_events': daily_events,
            'beans': beans_with_colors,
            'year': year,
            'month': month,
            'days_in_month': days_in_month,
            'calendar_start': calendar_start.strftime('%Y-%m-%d'),
            'calendar_end': calendar_end.strftime('%Y-%m-%d')
        })

    except Exception as e:
        logger.error("获取咖啡豆日历数据错误: %s", str(e), exc_info=True)
        return Response(
            {'error': str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['GET'])
@permission_classes([IsAuthenticated])
@ios_cache_response('filtered_brewlog_list')
def filtered_brewlog_list(request):
    """获取筛选过的冲煮记录列表"""
    logger.info("开始处理筛选冲煮记录列表请求")
    try:
        # 获取所有筛选参数
        filters = {}

        # 日期范围
        date_from = request.query_params.get('date_from')
        if date_from:
            filters['date_from'] = date_from

        date_to = request.query_params.get('date_to')
        if date_to:
            filters['date_to'] = date_to

        # 搜索关键词
        search_query = request.query_params.get('search_query')
        if search_query:
            filters['search_query'] = search_query

        # 冲煮方式
        brew_method = request.query_params.get('brew_method')
        if brew_method:
            filters['brew_method'] = brew_method

        # 咖啡豆
        coffee_bean = request.query_params.get('coffee_bean')
        if coffee_bean:
            filters['coffee_bean'] = coffee_bean

        # 评分范围
        rating_range = request.query_params.get('rating_range')
        if rating_range:
            filters['rating_range'] = rating_range

        # 页码
        page = int(request.query_params.get('page', 1))
        # 每页记录数
        per_page = int(request.query_params.get('page_size', 20))

        logger.debug(f"筛选条件: {filters}")
        logger.debug(f"页码: {page}, 每页记录数: {per_page}")

        # 使用BrewingRecordManager的get_filtered_records方法获取筛选后的记录
        result = BrewingRecord.objects.get_filtered_records(
            user=request.user,
            filters=filters,
            page=page,
            per_page=per_page
        )

        # 序列化数据
        records_serializer = IOSBrewingRecordSerializer(result['records'], many=True)
        data = records_serializer.data

        # 检查每条记录的coffee_bean字段是否有id
        for record in data:
            if 'coffee_bean' not in record or not isinstance(record['coffee_bean'], dict) or 'id' not in record['coffee_bean']:
                # 如果没有id，添加默认的coffee_bean对象
                record['coffee_bean'] = {
                    'id': 0,
                    'name': '',
                    'roaster': '',
                    'origin': '',
                    'process': '',
                    'type': 'SINGLE',
                    'type_display': '单品',
                    'roast_level': 3,
                    'roast_level_display': '中浅烘',
                    'description': '',
                    'price': None,
                    'weight': None,
                    'purchase_date': None,
                    'roast_date': None,
                    'is_finished': False,
                    'created_at': None,
                    'is_favorite': False,
                    'is_decaf': False,
                    'is_archived': False,
                    'is_deleted': False,
                    'altitude_type': 'SINGLE',
                    'altitude_single': None,
                    'altitude_min': None,
                    'altitude_max': None,
                    'region': '',
                    'finca': '',
                    'variety': '',
                    'barcode': '',
                    'rest_period_min': None,
                    'rest_period_max': None,
                    'rest_period_progress': None,
                    'stock_status': '充足',
                    'deleted_at': None,
                    'bag_remain': None,
                    'notes': '',
                    'taste_notes': []
                }

        # 构建响应数据
        response_data = {
            'count': result['total_count'],
            'next': page < result['total_pages'],
            'previous': page > 1,
            'total_pages': result['total_pages'],
            'current_page': page,
            'results': data,
            'filters': filters
        }

        logger.info(f"成功获取筛选冲煮记录列表，共 {len(data)} 条记录")
        return Response(response_data)
    except Exception as e:
        logger.error(f"处理筛选冲煮记录列表请求时发生错误: {str(e)}", exc_info=True)
        return Response(
            {'error': '获取筛选冲煮记录列表失败'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['DELETE'])
@permission_classes([IsAuthenticated])
@csrf_exempt  # 豁免CSRF验证，使用JWT认证代替
def delete_brewlog(request, record_id):
    """删除指定的冲煮记录"""
    logger.info(f"用户 {request.user.username} 请求删除冲煮记录 {record_id}")

    try:
        # 查找记录并确保它属于当前用户
        record = BrewingRecord.objects.filter(id=record_id, user=request.user).first()

        if not record:
            logger.warning(f"找不到记录ID={record_id}或记录不属于用户ID={request.user.id}")
            return Response({
                'success': False,
                'error': '找不到指定的冲煮记录'
            }, status=status.HTTP_404_NOT_FOUND)

        # 执行删除操作 (库存回退和缓存更新逻辑已移至模型的delete方法)
        record.delete()

        # 清除相关的缓存 - 删除冲煮记录会影响配方使用统计
        invalidate_ios_cache('brewlog_list', request.user.id)
        invalidate_ios_cache('brewlog_statistics', request.user.id)
        invalidate_ios_cache('filtered_brewlog_list', request.user.id)
        invalidate_ios_cache('recipe_list', request.user.id)          # 配方册缓存（使用次数、最后使用时间）
        invalidate_ios_cache('heatmap_data', request.user.id)
        invalidate_ios_cache('hindsight_data', request.user.id)

        logger.info(f"成功删除冲煮记录 {record_id}")
        return Response({
            'success': True,
            'message': '冲煮记录已成功删除',
            'record_id': record_id
        })

    except Exception as e:
        logger.error(f"删除冲煮记录 {record_id} 时发生错误: {str(e)}", exc_info=True)
        return Response({
            'success': False,
            'error': f'删除记录时出错: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['POST'])
@permission_classes([AllowAny])
def token_refresh(request):
    """刷新JWT令牌"""
    logger.info("开始处理令牌刷新请求")

    # 记录请求数据（不记录敏感信息）
    if hasattr(request, 'data') and 'refresh' in request.data:
        refresh_token_preview = request.data['refresh'][:10] + '...' if len(request.data['refresh']) > 10 else '***'
        logger.info(f"收到刷新令牌: {refresh_token_preview}")

    refresh_token = request.data.get('refresh')
    device_id = request.data.get('device_id')
    device_data = {
        'device_model': request.data.get('device_model'),
        'device_os_version': request.data.get('device_os_version'),
        'app_version': request.data.get('app_version')
    }

    try:
        # 检查刷新令牌
        if not refresh_token:
            return Response({
                'error': '缺少刷新令牌',
                'code': 'token_refresh_missing'
            }, status=status.HTTP_400_BAD_REQUEST)

        try:
            # 使用模型层方法处理令牌刷新
            result = IOSDevice.objects.refresh_token(
                refresh_token=refresh_token,
                device_id=device_id,
                device_data=device_data
            )
            return Response(result)

        except TokenError as e:
            logger.warning(f"令牌刷新失败 - 无效的刷新令牌: {str(e)}")
            return Response({
                'error': '无效的刷新令牌',
                'code': 'token_invalid',
                'detail': str(e)
            }, status=status.HTTP_401_UNAUTHORIZED)
    except Exception as e:
        logger.error(f"令牌刷新时发生意外错误: {str(e)}", exc_info=True)
        return Response({
            'error': '令牌刷新失败，请重新登录',
            'code': 'token_refresh_error',
            'detail': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def token_verify(request):
    """验证JWT令牌的有效性"""
    logger.info(f"用户 {request.user.username} (ID: {request.user.id}) 验证令牌有效性")

    device_id = request.data.get('device_id')

    try:
        # 使用模型层方法验证令牌
        result = IOSDevice.verify_token(request.user, device_id)
        return Response(result)
    except Exception as e:
        logger.error(f"验证令牌时发生错误: {str(e)}", exc_info=True)
        return Response({
            'valid': False,
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def check_onboarding_status(request):
    """检查用户是否需要新手引导"""
    try:
        from my.models import Equipment
        needs_onboarding = Equipment.needs_onboarding(request.user)

        logger.info(f"用户 {request.user.username} (ID: {request.user.id}) 新手引导状态检查: {needs_onboarding}")

        return Response({
            'needs_onboarding': needs_onboarding
        })
    except Exception as e:
        logger.error(f"检查新手引导状态时发生错误: {str(e)}", exc_info=True)
        return Response({
            'error': f'检查失败: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def onboarding_submit(request):
    """处理iOS端新手引导表单提交"""
    try:
        from my.models import CoffeeBean, Equipment
        from django.utils import timezone

        # 检查是否已经存在相关记录
        if CoffeeBean.objects.filter(user=request.user).exists():
            return Response({
                'success': False,
                'message': '您已经创建过咖啡豆记录，请直接使用现有记录或创建新记录'
            }, status=status.HTTP_400_BAD_REQUEST)

        if Equipment.objects.filter(
            user=request.user,
            type__in=['BREWER', 'GRINDER']
        ).exists():
            return Response({
                'success': False,
                'message': '您已经创建过设备记录，请直接使用现有记录或创建新记录'
            }, status=status.HTTP_400_BAD_REQUEST)

        # 获取表单数据
        bean_name = request.data.get('beanName', '').strip()
        roaster_type = request.data.get('roasterType', '')
        brew_method = request.data.get('brewMethod', '')
        grinder_type = request.data.get('grinderType', '')

        # 验证必填字段
        if not bean_name:
            return Response({
                'success': False,
                'message': '请输入咖啡豆名称'
            }, status=status.HTTP_400_BAD_REQUEST)

        if not roaster_type:
            return Response({
                'success': False,
                'message': '请选择咖啡豆来源'
            }, status=status.HTTP_400_BAD_REQUEST)

        if not brew_method:
            return Response({
                'success': False,
                'message': '请选择冲煮方式'
            }, status=status.HTTP_400_BAD_REQUEST)

        if not grinder_type:
            return Response({
                'success': False,
                'message': '请选择磨豆机类型'
            }, status=status.HTTP_400_BAD_REQUEST)

        # 创建咖啡豆记录
        bean = CoffeeBean.objects.create(
            user=request.user,
            type='SKIP',
            roaster='自家烘焙' if roaster_type == 'home' else '初始豆商',
            name=bean_name,
            roast_level=4,
            is_favorite=True,
            created_at=timezone.now().replace(microsecond=0)
        )

        # 创建冲煮器具记录
        brewer = Equipment.objects.create(
            user=request.user,
            type='BREWER',
            name='我的冲煮器具',
            brew_method=brew_method,
            is_favorite=True,
            created_at=timezone.now().replace(microsecond=0)
        )

        # 创建磨豆机记录
        grinder_name_map = {
            'dedicated': '我的磨豆机',
            'built_in': '研磨一体',
            'store': '商家代磨'
        }
        grinder_name = grinder_name_map.get(grinder_type, '我的磨豆机')

        grinder = Equipment.objects.create(
            user=request.user,
            type='GRINDER',
            name=grinder_name,
            grinder_purpose='ALL_PURPOSE',
            is_favorite=True,
            created_at=timezone.now().replace(microsecond=0)
        )

        # 清除相关缓存
        try:
            from iosapp.cache_utils import invalidate_ios_cache
            invalidate_ios_cache('equipment_list', request.user.id)
            invalidate_ios_cache('bean_list', request.user.id)
        except ImportError:
            logger.warning(f"无法清除iOS缓存：iosapp模块不可用 - 用户ID: {request.user.id}")

        logger.info(f"用户 {request.user.username} (ID: {request.user.id}) 完成新手引导设置")

        return Response({
            'success': True,
            'message': '初始数据创建成功'
        })

    except Exception as e:
        logger.error(f"新手引导提交失败: {str(e)}", exc_info=True)
        return Response({
            'success': False,
            'message': f'创建失败：{str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def token_blacklist(request):
    """将令牌加入黑名单（登出）"""
    try:
        logger.info(f"用户 {request.user.username} 请求登出")
        refresh_token = request.data.get('refresh')
        device_id = request.data.get('device_id')
        blacklist_device = request.data.get('blacklist_device', False)

        if not refresh_token:
            return Response({
                'error': '需要提供刷新令牌'
            }, status=status.HTTP_400_BAD_REQUEST)

        # 使用模型层方法处理登出
        IOSDevice.logout_device(
            user=request.user,
            refresh_token=refresh_token,
            device_id=device_id,
            blacklist_device=blacklist_device
        )

        return Response({
            'success': True,
            'message': '成功登出'
        })
    except TokenError as e:
        logger.warning(f"令牌黑名单处理错误: {str(e)}")
        return Response({
            'error': '无效的刷新令牌'
        }, status=status.HTTP_400_BAD_REQUEST)
    except Exception as e:
        logger.error(f"登出处理错误: {str(e)}", exc_info=True)
        return Response({
            'error': '登出处理失败'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET', 'PUT'])
@permission_classes([IsAuthenticated])
def user_profile(request):
    """获取或更新用户资料"""
    if request.method == 'GET':
        # 获取用户信息
        try:
            user = request.user
            return Response({
                'id': user.id,
                'username': user.username,
                'email': user.email,
                'first_name': user.first_name,
            })
        except Exception as e:
            logger.error(f"获取用户信息失败: {str(e)}", exc_info=True)
            return Response({
                'error': '获取用户信息失败'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    elif request.method == 'PUT':
        # 更新用户昵称
        try:
            user = request.user
            first_name = request.data.get('first_name', '')

            # 验证昵称长度
            if not first_name:
                return Response({
                    'error': '昵称不能为空'
                }, status=status.HTTP_400_BAD_REQUEST)

            if len(first_name) > 12:
                return Response({
                    'error': '昵称长度不能超过12个字符'
                }, status=status.HTTP_400_BAD_REQUEST)

            # 更新用户昵称
            user.first_name = first_name
            user.save()

            # 同步更新微信昵称，但不要反向同步回用户昵称
            from wechat.models import WechatUser
            wechat_user = WechatUser.objects.filter(user=user).first()
            if wechat_user:
                wechat_user.nickname = first_name
                # 使用sync_to_user=False参数避免循环同步
                if hasattr(wechat_user, 'save') and callable(getattr(wechat_user.save, '__call__', None)):
                    try:
                        wechat_user.save(sync_to_user=False)
                    except TypeError:
                        # 如果save方法不接受sync_to_user参数，则使用默认参数
                        wechat_user.save()

            # 清除用户资料相关缓存
            invalidate_ios_cache('user_profile', user.id)

            logger.info(f"用户 {user.username} 更新昵称为: {first_name}")
            return Response({
                'success': True,
                'message': '昵称更新成功',
                'first_name': first_name
            })

        except Exception as e:
            logger.error(f"更新用户昵称失败: {str(e)}", exc_info=True)
            return Response({
                'error': f'更新用户昵称失败: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
@ios_cache_response('user_devices')
def user_devices(request):
    """获取当前用户的iOS设备列表"""
    logger.info("开始处理用户设备列表请求")
    try:
        # 获取用户的设备记录
        devices = IOSDevice.objects.get_user_devices(request.user)

        # 使用序列化器
        serializer = IOSDeviceSerializer(devices, many=True)
        device_list = serializer.data

        return Response({
            'count': len(device_list),
            'devices': device_list
        })

    except Exception as e:
        logger.error(f"处理用户设备列表请求时发生错误: {str(e)}", exc_info=True)
        return Response(
            {'error': '获取设备列表失败'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def blacklist_device(request, device_id=None):
    """将指定设备加入黑名单"""
    try:
        # 优先使用路径参数，其次使用请求体
        if not device_id:
            device_id = request.data.get('device_id')

        if not device_id:
            return Response({
                'error': '需要提供设备ID'
            }, status=status.HTTP_400_BAD_REQUEST)

        # 查找设备并确保它属于当前用户
        device = IOSDevice.objects.filter(
            device_id=device_id,
            user=request.user
        ).first()

        if not device:
            return Response({
                'error': '找不到指定的设备或设备不属于当前用户'
            }, status=status.HTTP_404_NOT_FOUND)

        # 将设备加入黑名单
        device.blacklist()

        logger.info(f"设备已被加入黑名单 - 设备ID: {device_id}, 用户: {request.user.username}")
        return Response({
            'success': True,
            'message': '设备已被成功加入黑名单'
        })
    except Exception as e:
        logger.error(f"将设备加入黑名单时发生错误: {str(e)}", exc_info=True)
        return Response({
            'error': f'操作失败: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET', 'PUT', 'DELETE'])
@permission_classes([IsAuthenticated])
@csrf_exempt  # 豁免CSRF验证，使用JWT认证代替
def bean_detail(request, bean_id):
    """咖啡豆详情、更新和删除"""
    logger.info(f"处理咖啡豆请求 - 方法: {request.method}, Bean ID: {bean_id}")

    try:
        # 检索咖啡豆对象
        bean = CoffeeBean.objects.get(id=bean_id, user=request.user)

        # 处理请求
        if request.method == 'GET':
            # 序列化咖啡豆数据
            serializer = IOSCoffeeBeanSerializer(bean)

            # 构建响应数据
            response_data = serializer.data

            # 添加拼配组件信息
            if bean.type == 'BLEND':
                blend_components = bean.get_blend_components()
                if blend_components:
                    response_data['blend_components'] = IOSBlendComponentSerializer(blend_components, many=True).data

            # 添加回购记录信息
            occurrences = BeanOccurrence.objects.filter(coffee_bean=bean)
            if occurrences.exists():
                response_data['occurrences'] = IOSBeanOccurrenceSerializer(occurrences, many=True).data

            response = Response(response_data)
            # 添加最后更新时间HTTP头
            response['X-Updated-At'] = str(int(bean.created_at.timestamp()))
            return response

        elif request.method == 'PUT':
            # 预处理请求数据
            data = request.data.copy()

            # 记录详细的请求数据日志用于调试
            logger.info(f"更新咖啡豆请求 - Bean ID: {bean_id}, 数据: {data}")
            if 'roast_date' in data:
                logger.info(f"roast_date值: {data['roast_date']}, 类型: {type(data['roast_date'])}")

            # 处理roast_date字段
            if 'roast_date' in data and data['roast_date']:
                try:
                    # 详细记录roast_date的值和类型
                    logger.info(f"正在处理roast_date: {data['roast_date']}, 类型: {type(data['roast_date'])}")

                    # 尝试解析时间戳格式
                    if isinstance(data['roast_date'], (int, float)):
                        timestamp = float(data['roast_date'])
                        # 只记录，不立即转换，保持时间戳格式供序列化器使用
                        logger.info(f"时间戳格式验证通过: {timestamp}")
                    elif isinstance(data['roast_date'], str):
                        # 检查是否为时间戳字符串
                        if data['roast_date'].replace('.', '', 1).isdigit():
                            # 是时间戳字符串，直接保存数值
                            data['roast_date'] = float(data['roast_date'])
                            logger.info(f"字符串时间戳转换为数值: {data['roast_date']}")
                        else:
                            # 是日期字符串，如"2025-05-08"，转换为时间戳
                            try:
                                # 先尝试使用parse_date解析
                                parsed_date = parse_date(data['roast_date'])
                                if parsed_date:
                                    # 将日期转换为时间戳
                                    dt = datetime.combine(parsed_date, datetime.min.time())
                                    from django.utils import timezone
                                    dt = timezone.make_aware(dt)
                                    data['roast_date'] = dt.timestamp()
                                    logger.info(f"日期字符串转换为时间戳: {data['roast_date']}")
                                else:
                                    # 尝试直接用datetime解析
                                    date_obj = datetime.strptime(data['roast_date'], '%Y-%m-%d').date()
                                    dt = datetime.combine(date_obj, datetime.min.time())
                                    from django.utils import timezone
                                    dt = timezone.make_aware(dt)
                                    data['roast_date'] = dt.timestamp()
                                    logger.info(f"日期字符串通过strptime转换为时间戳: {data['roast_date']}")
                            except ValueError as ve:
                                logger.warning(f"日期字符串格式解析失败: {data['roast_date']}, 错误: {str(ve)}")
                                # 解析失败，从数据中移除该字段
                                data.pop('roast_date')
                except Exception as e:
                    # 如果解析错误，记录日志并移除字段
                    logger.warning(f"无法解析烘焙日期: {data['roast_date']}, 错误: {str(e)}, 类型: {type(data['roast_date'])}")
                    data.pop('roast_date')

            # 更新咖啡豆信息
            serializer = IOSCoffeeBeanSerializer(bean, data=data, partial=True)
            if serializer.is_valid():
                serializer.save()

                # 手动处理bagWeight和purchasePrice字段
                if 'bag_weight' in data and data['bag_weight'] is not None:
                    bean.bag_weight = data['bag_weight']
                    logger.info(f"手动更新bag_weight字段: {bean.bag_weight}")

                if 'purchase_price' in data and data['purchase_price'] is not None:
                    bean.purchase_price = data['purchase_price']
                    logger.info(f"手动更新purchase_price字段: {bean.purchase_price}")

                if 'bag_remain' in data and data['bag_remain'] is not None:
                    bean.bag_remain = data['bag_remain']
                    logger.info(f"手动更新bag_remain字段: {bean.bag_remain}")

                # 如果手动更新了任何字段，再次保存bean对象
                if any(field in data for field in ['bag_weight', 'purchase_price', 'bag_remain']):
                    bean.save()
                    logger.info(f"已保存手动更新的字段")

                # 处理风味标签
                if 'taste_notes' in data and data['taste_notes']:
                    tags = data['taste_notes']
                    logger.info(f"处理风味标签: {tags}, 类型: {type(tags)}")

                    # 先清除现有标签
                    bean.flavor_tags.clear()

                    # 尝试转换数据格式
                    if isinstance(tags, str):
                        try:
                            # 尝试解析JSON字符串
                            import json
                            tags = json.loads(tags)
                            logger.info(f"将字符串转换为JSON: {tags}, 类型: {type(tags)}")
                        except json.JSONDecodeError:
                            # 如果不是JSON，尝试以逗号分隔
                            tags = [tag.strip() for tag in tags.split(',') if tag.strip()]
                            logger.info(f"将字符串以逗号分隔: {tags}")

                    # 处理单个标签情况
                    if not isinstance(tags, (list, tuple)) and tags:
                        tags = [str(tags)]
                        logger.info(f"单个标签转换为列表: {tags}")

                    for tag_name in tags:
                        try:
                            tag_name = str(tag_name).strip()
                            if not tag_name:
                                continue

                            tag, created = FlavorTag.objects.get_or_create(
                                name=tag_name,
                                user=request.user
                            )
                            bean.flavor_tags.add(tag)
                            logger.info(f"添加风味标签: {tag_name}, 创建: {created}")
                        except Exception as e:
                            logger.error(f"添加风味标签 {tag_name} 时出错: {str(e)}")

                    logger.info(f"已添加 {bean.flavor_tags.count()} 个风味标签")

                # 处理拼配组件
                if bean.type == 'BLEND' and 'blend_components' in data and data['blend_components']:
                    # 先删除现有组件
                    bean.blend_components.all().delete()

                    components = data['blend_components']
                    logger.info(f"处理拼配组件: {len(components)} 个组件")
                    for i, comp_data in enumerate(components):
                        BlendComponent.objects.create(
                            coffee_bean=bean,
                            origin=comp_data.get('origin', ''),
                            region=comp_data.get('region', ''),
                            finca=comp_data.get('finca', ''),
                            variety=comp_data.get('variety', ''),
                            process=comp_data.get('process', ''),
                            roast_level=comp_data.get('roast_level', 4),
                            blend_ratio=comp_data.get('blend_ratio', 0),
                            altitude_type=comp_data.get('altitude_type', 'SINGLE'),
                            altitude_single=comp_data.get('altitude_single'),
                            altitude_min=comp_data.get('altitude_min'),
                            altitude_max=comp_data.get('altitude_max'),
                            order=i
                        )

                # 清除所有与咖啡豆相关的iOS缓存
                invalidate_ios_cache('bean_list', request.user.id)
                invalidate_ios_cache('bean_calendar_data', request.user.id)
                logger.info(f"咖啡豆更新成功，已清除相关缓存 - Bean ID: {bean_id}")

                current_time = int(time.time())
                response = Response({
                    'success': True,
                    'message': '咖啡豆更新成功',
                    'data': serializer.data,
                    'updated_at': current_time
                })
                # 添加更新时间戳HTTP头
                response['X-Updated-At'] = str(current_time)
                return response
            else:
                logger.warning(f"咖啡豆数据验证失败 - 错误: {serializer.errors}")
                return Response({
                    'error': '数据验证失败',
                    'details': serializer.errors
                }, status=status.HTTP_400_BAD_REQUEST)

        elif request.method == 'DELETE':
            # 软删除咖啡豆
            bean.is_deleted = True
            bean.deleted_at = timezone.now()
            bean.save()

            # 清除所有与咖啡豆相关的iOS缓存
            invalidate_ios_cache('bean_list', request.user.id)
            invalidate_ios_cache('bean_calendar_data', request.user.id)
            logger.info(f"咖啡豆删除成功，已清除相关缓存 - Bean ID: {bean_id}, 名称: {bean.name}")

            current_time = int(time.time())
            response = Response({
                'success': True,
                'message': '咖啡豆删除成功',
                'updated_at': current_time
            })
            # 添加更新时间戳HTTP头
            response['X-Updated-At'] = str(current_time)
            return response

    except CoffeeBean.DoesNotExist:
        return Response({"error": "咖啡豆不存在或您无权访问"}, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        logger.error(f"处理咖啡豆请求时发生错误: {str(e)}", exc_info=True)
        return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['POST'])
@permission_classes([IsAuthenticated])
@csrf_exempt  # 豁免CSRF验证，使用JWT认证代替
def toggle_favorite_bean(request, bean_id):
    """切换咖啡豆的收藏状态"""
    logger.info(f"开始处理切换咖啡豆收藏状态请求 - Bean ID: {bean_id}")
    try:
        # 查找咖啡豆
        bean = CoffeeBean.objects.filter(
            id=bean_id,
            user=request.user,
            is_deleted=False
        ).first()

        if not bean:
            logger.warning(f"找不到指定的咖啡豆 - Bean ID: {bean_id}, 用户: {request.user.username}")
            return Response({
                'error': '找不到指定的咖啡豆'
            }, status=status.HTTP_404_NOT_FOUND)

        # 切换收藏状态逻辑
        if bean.is_favorite:
            # 如果当前已是收藏状态，则取消收藏
            bean.is_favorite = False
            bean.save()
            status_str = "取消收藏"
            logger.info(f"成功{status_str}咖啡豆 - Bean ID: {bean_id}, 名称: {bean.name}")
        else:
            # 如果当前未收藏，则先取消其他收藏的咖啡豆
            CoffeeBean.objects.filter(
                user=request.user,
                is_favorite=True
            ).update(is_favorite=False)

            # 然后设置当前咖啡豆为收藏
            bean.is_favorite = True
            bean.save()
            status_str = "收藏"
            logger.info(f"成功{status_str}咖啡豆 - Bean ID: {bean_id}, 名称: {bean.name}")

        # 清除所有与咖啡豆相关的iOS缓存
        invalidate_ios_cache('bean_list', request.user.id)
        invalidate_ios_cache('bean_calendar_data', request.user.id)
        logger.info(f"已清除用户 {request.user.id} 的咖啡豆相关缓存")

        # 获取最新的咖啡豆列表数据以返回给客户端
        beans = CoffeeBean.objects.filter(
            user=request.user,
            is_deleted=False
        ).order_by('-created_at')

        # 使用分页器
        paginator = PageNumberPagination()
        paginator.page_size = 20
        page = paginator.paginate_queryset(beans, request)

        if page is not None:
            serializer = IOSCoffeeBeanSerializer(page, many=True)
            updated_beans = serializer.data
        else:
            updated_beans = []

        current_time = int(time.time())
        response = Response({
            'success': True,
            'message': f'已{status_str}咖啡豆',
            'is_favorite': bean.is_favorite,
            'updated_beans': updated_beans,
            'updated_at': current_time
        })
        # 添加更新时间戳HTTP头
        response['X-Updated-At'] = str(current_time)
        return response

    except Exception as e:
        logger.error(f"切换咖啡豆收藏状态时发生错误: {str(e)}", exc_info=True)
        return Response({
            'error': f'操作失败: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['POST'])
@permission_classes([IsAuthenticated])
@csrf_exempt  # 豁免CSRF验证，使用JWT认证代替
def archive_bean(request, bean_id):
    """归档咖啡豆"""
    logger.info(f"开始处理归档咖啡豆请求 - Bean ID: {bean_id}")
    try:
        # 查找咖啡豆
        bean = CoffeeBean.objects.filter(
            id=bean_id,
            user=request.user,
            is_deleted=False
        ).first()

        if not bean:
            logger.warning(f"找不到指定的咖啡豆 - Bean ID: {bean_id}, 用户: {request.user.username}")
            return Response({
                'error': '找不到指定的咖啡豆'
            }, status=status.HTTP_404_NOT_FOUND)

        # 归档咖啡豆，并自动取消首选状态
        bean.is_archived = True
        if bean.is_favorite:
            bean.is_favorite = False
        bean.save()

        # 清除所有与咖啡豆相关的iOS缓存
        invalidate_ios_cache('bean_list', request.user.id)
        invalidate_ios_cache('bean_calendar_data', request.user.id)
        logger.info(f"成功归档咖啡豆并清除相关缓存 - Bean ID: {bean_id}, 名称: {bean.name}")

        current_time = int(time.time())
        response = Response({
            'success': True,
            'message': '已归档咖啡豆',
            'is_archived': True,
            'updated_at': current_time
        })
        # 添加更新时间戳HTTP头
        response['X-Updated-At'] = str(current_time)
        return response

    except Exception as e:
        logger.error(f"归档咖啡豆时发生错误: {str(e)}", exc_info=True)
        return Response({
            'error': f'操作失败: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['POST'])
@permission_classes([IsAuthenticated])
@csrf_exempt  # 豁免CSRF验证，使用JWT认证代替
def unarchive_bean(request, bean_id):
    """取消归档咖啡豆"""
    logger.info(f"开始处理取消归档咖啡豆请求 - Bean ID: {bean_id}")
    try:
        # 查找咖啡豆
        bean = CoffeeBean.objects.filter(
            id=bean_id,
            user=request.user,
            is_deleted=False
        ).first()

        if not bean:
            logger.warning(f"找不到指定的咖啡豆 - Bean ID: {bean_id}, 用户: {request.user.username}")
            return Response({
                'error': '找不到指定的咖啡豆'
            }, status=status.HTTP_404_NOT_FOUND)

        # 取消归档咖啡豆
        bean.is_archived = False
        bean.save()

        # 清除所有与咖啡豆相关的iOS缓存
        invalidate_ios_cache('bean_list', request.user.id)
        invalidate_ios_cache('bean_calendar_data', request.user.id)
        logger.info(f"成功取消归档咖啡豆并清除相关缓存 - Bean ID: {bean_id}, 名称: {bean.name}")

        current_time = int(time.time())
        response = Response({
            'success': True,
            'message': '已取消归档咖啡豆',
            'is_archived': False,
            'updated_at': current_time
        })
        # 添加更新时间戳HTTP头
        response['X-Updated-At'] = str(current_time)
        return response

    except Exception as e:
        logger.error(f"取消归档咖啡豆时发生错误: {str(e)}", exc_info=True)
        return Response({
            'error': f'操作失败: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['POST'])
@permission_classes([IsAuthenticated])
@csrf_exempt  # 豁免CSRF验证，使用JWT认证代替
def create_bean(request):
    """
    创建新的咖啡豆
    """
    try:
        data = request.data
        logger.info(f"创建咖啡豆请求 - 数据: {ensure_serializable(data)}")

        # 详细日志记录
        logger.info(f"请求Content-Type: {request.content_type}")
        logger.info(f"请求方法: {request.method}")

        # 特别检查flavor_tags字段
        if 'flavor_tags' in data:
            logger.info(f"收到的flavor_tags: {data['flavor_tags']}, 类型: {type(data['flavor_tags'])}")
        else:
            logger.warning("请求数据中缺少flavor_tags字段")

        # 检查请求头
        headers = dict(request.headers.items())
        safe_headers = {k: v for k, v in headers.items() if not k.lower() in ['authorization', 'cookie']}
        logger.info(f"请求头: {safe_headers}")

        # 必需的字段
        required_fields = ['name', 'type', 'roaster', 'roast_level']
        for field in required_fields:
            if field not in data or not data[field]:
                return Response({'error': f'缺少必需字段: {field}'}, status=400)

        # 检查用户是否已有相同名称的咖啡豆（同一个用户不能有相同名称的豆子）
        if CoffeeBean.objects.filter(user=request.user, name=data['name'], is_deleted=False).exists():
            return Response({'error': f'您已创建过名为"{data["name"]}"的咖啡豆'}, status=400)

        # 创建咖啡豆对象
        bean = CoffeeBean(
            user=request.user,
            name=data['name'],
            type=data['type'],
            roaster=data['roaster'],
            roast_level=data['roast_level'],
            origin=data.get('origin', ''),
            region=data.get('region', ''),
            finca=data.get('finca', ''),
            variety=data.get('variety', ''),
            process=data.get('process', ''),
            barcode=data.get('barcode', ''),
            notes=data.get('notes', ''),
            is_decaf=data.get('is_decaf', False),
            is_archived=data.get('is_archived', False),
            is_favorite=data.get('is_favorite', False)
        )

        # 可选字段
        if 'purchase_price' in data and data['purchase_price']:
            bean.purchase_price = data['purchase_price']

        if 'bag_weight' in data and data['bag_weight']:
            bean.bag_weight = data['bag_weight']

        if 'bag_remain' in data and data['bag_remain']:
            bean.bag_remain = data['bag_remain']

        if 'created_at' in data and data['created_at']:
            try:
                # 尝试解析时间戳格式
                timestamp = float(data['created_at'])
                bean.created_at = datetime.fromtimestamp(timestamp, tz=timezone.utc).replace(microsecond=0)
                # 同时设置initial_created_at
                bean.initial_created_at = bean.created_at
                logger.info(f"创建咖啡豆时设置initial_created_at = created_at: {bean.created_at}")
            except (ValueError, TypeError):
                try:
                    # 尝试解析字符串日期格式 (YYYY-MM-DD)
                    bean.created_at = timezone.make_aware(datetime.strptime(data['created_at'], '%Y-%m-%d')).replace(microsecond=0)
                    # 同时设置initial_created_at
                    bean.initial_created_at = bean.created_at
                    logger.info(f"创建咖啡豆时设置initial_created_at = created_at: {bean.created_at}")
                except:
                    # 如果日期解析错误，使用当前时间
                    bean.created_at = timezone.now().replace(microsecond=0)
                    # 同时设置initial_created_at
                    bean.initial_created_at = bean.created_at
                    logger.info(f"创建咖啡豆时设置initial_created_at = created_at(当前时间): {bean.created_at}")
        else:
            # 确保即使没有提供日期，也会去除微秒
            bean.created_at = timezone.now().replace(microsecond=0)
            # 同时设置initial_created_at
            bean.initial_created_at = bean.created_at
            logger.info(f"创建咖啡豆时设置initial_created_at = created_at(当前时间): {bean.created_at}")

        # 处理烘焙日期
        if 'roast_date' in data and data['roast_date']:
            try:
                # 详细记录roast_date的值和类型
                logger.info(f"create_bean中处理roast_date: {data['roast_date']}, 类型: {type(data['roast_date'])}")

                # 尝试解析时间戳格式
                if isinstance(data['roast_date'], (int, float)):
                    # 直接使用时间戳
                    timestamp = float(data['roast_date'])
                    bean.roast_date = datetime.fromtimestamp(timestamp).date()
                    logger.info(f"时间戳转换为日期: {bean.roast_date}")
                elif isinstance(data['roast_date'], str):
                    # 检查是否为时间戳字符串
                    if data['roast_date'].replace('.', '', 1).isdigit():
                        # 是时间戳字符串
                        timestamp = float(data['roast_date'])
                        bean.roast_date = datetime.fromtimestamp(timestamp).date()
                        logger.info(f"字符串时间戳转换为日期: {bean.roast_date}")
                    else:
                        # 是日期字符串，如"2025-05-08"
                        try:
                            # 先尝试使用parse_date解析
                            date_obj = parse_date(data['roast_date'])
                            if date_obj:
                                bean.roast_date = date_obj
                                logger.info(f"parse_date解析成功: {bean.roast_date}")
                            else:
                                # 尝试直接用datetime解析
                                bean.roast_date = datetime.strptime(data['roast_date'], '%Y-%m-%d').date()
                                logger.info(f"strptime解析成功: {bean.roast_date}")
                        except ValueError as ve:
                            logger.warning(f"日期字符串格式解析失败: {data['roast_date']}, 错误: {str(ve)}")
                            bean.roast_date = None
            except Exception as e:
                # 如果解析错误，记录日志但不设置日期
                logger.warning(f"无法解析烘焙日期: {data['roast_date']}, 错误: {str(e)}, 类型: {type(data['roast_date'])}")
                bean.roast_date = None

        # 处理海拔信息
        altitude_type = data.get('altitude_type', 'SINGLE')
        bean.altitude_type = altitude_type

        if altitude_type == 'SINGLE' and 'altitude_single' in data and data['altitude_single']:
            bean.altitude_single = data['altitude_single']

        if altitude_type == 'RANGE':
            if 'altitude_min' in data and data['altitude_min']:
                bean.altitude_min = data['altitude_min']
            if 'altitude_max' in data and data['altitude_max']:
                bean.altitude_max = data['altitude_max']

        # 处理养豆期信息
        if 'rest_period_min' in data and data['rest_period_min'] is not None:
            bean.rest_period_min = data['rest_period_min']

        if 'rest_period_max' in data and data['rest_period_max'] is not None:
            bean.rest_period_max = data['rest_period_max']

        # 保存咖啡豆
        bean.save()
        logger.info(f"成功创建咖啡豆基本信息 - ID: {bean.id}, 名称: {bean.name}")

        # 处理风味标签
        if 'flavor_tags' in data and data['flavor_tags']:
            tags = data['flavor_tags']
            logger.info(f"处理风味标签: {tags}, 类型: {type(tags)}")

            # 尝试转换数据格式
            if isinstance(tags, str):
                try:
                    # 尝试解析JSON字符串
                    import json
                    tags = json.loads(tags)
                    logger.info(f"将字符串转换为JSON: {tags}, 类型: {type(tags)}")
                except json.JSONDecodeError:
                    # 如果不是JSON，尝试以逗号分隔
                    tags = [tag.strip() for tag in tags.split(',') if tag.strip()]
                    logger.info(f"将字符串以逗号分隔: {tags}")

            # 处理单个标签情况
            if not isinstance(tags, (list, tuple)) and tags:
                tags = [str(tags)]
                logger.info(f"单个标签转换为列表: {tags}")

            if not tags:
                logger.warning("没有有效的风味标签要处理")

            for tag_name in tags:
                try:
                    tag_name = str(tag_name).strip()
                    if not tag_name:
                        continue

                    tag, created = FlavorTag.objects.get_or_create(
                        name=tag_name,
                        user=request.user
                    )
                    bean.flavor_tags.add(tag)
                    logger.info(f"添加风味标签: {tag_name}, 创建: {created}")
                except Exception as e:
                    logger.error(f"添加风味标签 {tag_name} 时出错: {str(e)}")

            logger.info(f"已添加 {bean.flavor_tags.count()} 个风味标签")

        # 处理拼配组件
        if bean.type == 'BLEND' and 'blend_components' in data and data['blend_components']:
            components = data['blend_components']
            logger.info(f"处理拼配组件: {len(components)} 个组件")
            for i, comp_data in enumerate(components):
                BlendComponent.objects.create(
                    coffee_bean=bean,
                    origin=comp_data.get('origin', ''),
                    region=comp_data.get('region', ''),
                    finca=comp_data.get('finca', ''),
                    variety=comp_data.get('variety', ''),
                    process=comp_data.get('process', ''),
                    roast_level=comp_data.get('roast_level', 4),
                    blend_ratio=comp_data.get('blend_ratio', 0),
                    altitude_type=comp_data.get('altitude_type', 'SINGLE'),
                    altitude_single=comp_data.get('altitude_single'),
                    altitude_min=comp_data.get('altitude_min'),
                    altitude_max=comp_data.get('altitude_max'),
                    order=i
                )

        # 清除缓存
        try:
            from my.cache_utils import invalidate_bean_cache
            invalidate_bean_cache(request.user.id)
        except ImportError:
            logging.warning(f"无法清除咖啡豆缓存：cache_utils模块不可用 - 用户ID: {request.user.id}")

        try:
            from iosapp.cache_utils import invalidate_ios_cache
            invalidate_ios_cache('bean_list', request.user.id)
        except ImportError:
            logging.warning(f"无法清除iOS缓存：iosapp模块不可用 - 用户ID: {request.user.id}")

        # 返回成功
        serializer = IOSCoffeeBeanSerializer(bean)
        return Response(serializer.data, status=201)

    except Exception as e:
        logging.error(f"创建咖啡豆失败：{str(e)}", exc_info=True)
        return Response({
            'error': f'创建咖啡豆失败: {str(e)}'
        }, status=500)

@api_view(['POST'])
@permission_classes([IsAuthenticated])
@csrf_exempt  # 豁免CSRF验证，使用JWT认证代替
def create_bean_occurrence(request, bean_id):
    """为指定咖啡豆创建回购记录"""
    logger.info(f"处理创建咖啡豆回购记录请求 - Bean ID: {bean_id}")

    try:
        # 查找咖啡豆
        bean = CoffeeBean.objects.filter(
            id=bean_id,
            user=request.user,
            is_deleted=False
        ).first()

        if not bean:
            logger.warning(f"找不到指定的咖啡豆 - Bean ID: {bean_id}, 用户: {request.user.username}")
            return Response({
                'error': '找不到指定的咖啡豆'
            }, status=status.HTTP_404_NOT_FOUND)

        # 验证输入数据
        serializer = IOSBeanOccurrenceSerializer(data=request.data)
        if serializer.is_valid():
            # 如果是第一次回购，保存当前值为初始值
            if not BeanOccurrence.objects.filter(coffee_bean=bean).exists():
                # 记录日志用于调试
                logger.info(f"iOS端首次回购 - Bean ID: {bean_id}, 保存初始值")
                logger.info(f"初始created_at值: {bean.created_at}")

                bean.initial_bag_weight = bean.bag_weight
                bean.initial_bag_remain = bean.bag_remain
                bean.initial_purchase_price = bean.purchase_price
                bean.initial_roast_date = bean.roast_date
                bean.initial_created_at = bean.created_at  # 添加这行，保存初始created_at值
                bean.initial_rest_period_min = bean.rest_period_min
                bean.initial_rest_period_max = bean.rest_period_max
                bean.save()

                # 记录保存后的值以验证
                refreshed_bean = CoffeeBean.objects.get(id=bean.id)
                logger.info(f"保存初始值后: initial_created_at={refreshed_bean.initial_created_at}, created_at={refreshed_bean.created_at}")

            # 处理created_at时间戳
            created_at_data = request.data.get('created_at')
            if created_at_data and isinstance(created_at_data, (int, float)):
                # 如果是数字类型时间戳，转换为日期时间对象
                created_at = datetime.fromtimestamp(created_at_data)
            else:
                created_at = timezone.now()

            # 确保时间格式统一(去除微秒)
            created_at = created_at.replace(microsecond=0)

            # 处理roast_date
            roast_date_data = request.data.get('roast_date')
            roast_date = None
            if roast_date_data:
                try:
                    # 详细记录roast_date的值和类型
                    logger.info(f"create_bean_occurrence中处理roast_date: {roast_date_data}, 类型: {type(roast_date_data)}")

                    if isinstance(roast_date_data, (int, float)):
                        # 直接使用时间戳
                        timestamp = float(roast_date_data)
                        roast_date = datetime.fromtimestamp(timestamp).date()
                        logger.info(f"时间戳转换为日期: {roast_date}")
                    elif isinstance(roast_date_data, str):
                        # 检查是否为时间戳字符串
                        if roast_date_data.replace('.', '', 1).isdigit():
                            # 是时间戳字符串
                            timestamp = float(roast_date_data)
                            roast_date = datetime.fromtimestamp(timestamp).date()
                            logger.info(f"字符串时间戳转换为日期: {roast_date}")
                        else:
                            # 是日期字符串，如"2025-05-08"
                            try:
                                # 先尝试使用parse_date解析
                                roast_date = parse_date(roast_date_data)
                                if roast_date:
                                    logger.info(f"parse_date解析成功: {roast_date}")
                                else:
                                    # 尝试直接用datetime解析
                                    roast_date = datetime.strptime(roast_date_data, '%Y-%m-%d').date()
                                    logger.info(f"strptime解析成功: {roast_date}")
                            except ValueError as ve:
                                logger.warning(f"日期字符串格式解析失败: {roast_date_data}, 错误: {str(ve)}")
                                roast_date = None
                except Exception as e:
                    logger.warning(f"解析烘焙日期出错: {str(e)}, 日期值: {roast_date_data}, 类型: {type(roast_date_data)}")
                    roast_date = None

            # 创建回购记录
            occurrence = BeanOccurrence(
                coffee_bean=bean,
                bag_weight=request.data.get('bag_weight'),
                bag_remain=request.data.get('bag_remain'),
                purchase_price=request.data.get('purchase_price'),
                roast_date=roast_date,
                created_at=created_at,
                rest_period_min=request.data.get('rest_period_min'),
                rest_period_max=request.data.get('rest_period_max')
            )
            occurrence.save()

            # 更新咖啡豆的包装信息
            bean.bag_weight = occurrence.bag_weight
            bean.bag_remain = occurrence.bag_remain
            bean.purchase_price = occurrence.purchase_price
            bean.roast_date = occurrence.roast_date
            bean.created_at = occurrence.created_at  # 更新咖啡豆的创建时间为最新回购时间
            bean.rest_period_min = occurrence.rest_period_min
            bean.rest_period_max = occurrence.rest_period_max
            bean.save()

            # 清除所有与咖啡豆相关的iOS缓存
            invalidate_ios_cache('bean_list', request.user.id)
            invalidate_ios_cache('bean_calendar_data', request.user.id)
            invalidate_ios_cache('hindsight_data', request.user.id)
            logger.info(f"回购记录创建成功，已清除相关缓存 - Bean ID: {bean_id}, 名称: {bean.name}")

            # 序列化并返回更新后的咖啡豆数据
            bean_data = IOSCoffeeBeanSerializer(bean).data
            occurrence_data = IOSBeanOccurrenceSerializer(occurrence).data

            current_time = int(time.time())
            response = Response({
                'success': True,
                'message': '回购记录创建成功',
                'bean': bean_data,
                'occurrence': occurrence_data,
                'updated_at': current_time
            })
            # 添加更新时间戳HTTP头
            response['X-Updated-At'] = str(current_time)
            return response
        else:
            logger.warning(f"回购记录数据验证失败 - 错误: {serializer.errors}")
            return Response({
                'error': '数据验证失败',
                'details': serializer.errors
            }, status=status.HTTP_400_BAD_REQUEST)

    except Exception as e:
        logger.error(f"创建回购记录时发生错误: {str(e)}", exc_info=True)
        return Response({
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['PUT', 'DELETE'])
@permission_classes([IsAuthenticated])
@csrf_exempt  # 豁免CSRF验证，使用JWT认证代替
def update_bean_occurrence(request, occurrence_id):
    """更新或删除咖啡豆回购记录"""
    logger.info(f"处理咖啡豆回购记录请求 - 方法: {request.method}, Occurrence ID: {occurrence_id}")

    try:
        # 查找回购记录
        occurrence = BeanOccurrence.objects.filter(
            id=occurrence_id,
            coffee_bean__user=request.user
        ).first()

        if not occurrence:
            logger.warning(f"找不到指定的回购记录 - Occurrence ID: {occurrence_id}, 用户: {request.user.username}")
            return Response({
                'error': '找不到指定的回购记录'
            }, status=status.HTTP_404_NOT_FOUND)

        # 获取关联的咖啡豆
        bean = occurrence.coffee_bean

        # 根据请求方法处理不同的操作
        if request.method == 'PUT':
            # 更新回购记录
            # 验证输入数据
            serializer = IOSBeanOccurrenceSerializer(occurrence, data=request.data, partial=True)
            if serializer.is_valid():
                # 处理created_at时间戳
                created_at_data = request.data.get('created_at')
                if created_at_data is not None:
                    if isinstance(created_at_data, (int, float)):
                        # 如果是数字类型时间戳，转换为日期时间对象
                        created_at = datetime.fromtimestamp(created_at_data)
                    elif isinstance(created_at_data, str):
                        try:
                            # 尝试ISO格式解析
                            created_at = datetime.fromisoformat(created_at_data.replace('Z', '+00:00'))
                        except ValueError:
                            # 保持原有值
                            created_at = occurrence.created_at
                    else:
                        # 保持原有值
                        created_at = occurrence.created_at
                else:
                    # 保持原有值
                    created_at = occurrence.created_at

                # 处理roast_date
                roast_date_data = request.data.get('roast_date')
                roast_date = None
                if roast_date_data:
                    try:
                        # 详细记录roast_date的值和类型
                        logger.info(f"update_bean_occurrence中处理roast_date: {roast_date_data}, 类型: {type(roast_date_data)}")

                        if isinstance(roast_date_data, (int, float)):
                            # 直接使用时间戳
                            timestamp = float(roast_date_data)
                            roast_date = datetime.fromtimestamp(timestamp).date()
                            logger.info(f"时间戳转换为日期: {roast_date}")
                        elif isinstance(roast_date_data, str):
                            # 检查是否为时间戳字符串
                            if roast_date_data.replace('.', '', 1).isdigit():
                                # 是时间戳字符串
                                timestamp = float(roast_date_data)
                                roast_date = datetime.fromtimestamp(timestamp).date()
                                logger.info(f"字符串时间戳转换为日期: {roast_date}")
                            else:
                                # 是日期字符串，如"2025-05-08"
                                try:
                                    # 先尝试使用parse_date解析
                                    roast_date = parse_date(roast_date_data)
                                    if roast_date:
                                        logger.info(f"parse_date解析成功: {roast_date}")
                                    else:
                                        # 尝试直接用datetime解析
                                        roast_date = datetime.strptime(roast_date_data, '%Y-%m-%d').date()
                                        logger.info(f"strptime解析成功: {roast_date}")
                                except ValueError as ve:
                                    logger.warning(f"日期字符串格式解析失败: {roast_date_data}, 错误: {str(ve)}")
                                    roast_date = None
                    except Exception as e:
                        logger.warning(f"解析烘焙日期出错: {str(e)}, 日期值: {roast_date_data}, 类型: {type(roast_date_data)}")
                        roast_date = None

                # 更新回购记录
                occurrence.bag_weight = request.data.get('bag_weight', occurrence.bag_weight)
                occurrence.bag_remain = request.data.get('bag_remain', occurrence.bag_remain)
                occurrence.purchase_price = request.data.get('purchase_price', occurrence.purchase_price)
                occurrence.roast_date = roast_date
                occurrence.created_at = created_at
                occurrence.rest_period_min = request.data.get('rest_period_min', occurrence.rest_period_min)
                occurrence.rest_period_max = request.data.get('rest_period_max', occurrence.rest_period_max)
                occurrence.save()

                # 检查此记录是否是最新的回购记录，如果是则更新咖啡豆信息
                latest_occurrence = bean.occurrences.order_by('-created_at').first()
                if occurrence.id == latest_occurrence.id:
                    bean.bag_weight = occurrence.bag_weight
                    bean.bag_remain = occurrence.bag_remain
                    bean.purchase_price = occurrence.purchase_price
                    bean.roast_date = occurrence.roast_date
                    bean.created_at = occurrence.created_at
                    bean.rest_period_min = occurrence.rest_period_min
                    bean.rest_period_max = occurrence.rest_period_max
                    bean.save()

                # 清除所有与咖啡豆相关的iOS缓存
                invalidate_ios_cache('bean_list', request.user.id)
                invalidate_ios_cache('bean_calendar_data', request.user.id)
                invalidate_ios_cache('hindsight_data', request.user.id)
                logger.info(f"回购记录更新成功，已清除相关缓存 - Occurrence ID: {occurrence_id}")

                # 序列化并返回更新后的咖啡豆数据
                bean_data = IOSCoffeeBeanSerializer(bean).data
                occurrence_data = IOSBeanOccurrenceSerializer(occurrence).data

                current_time = int(time.time())
                response = Response({
                    'success': True,
                    'message': '回购记录更新成功',
                    'bean': bean_data,
                    'occurrence': occurrence_data,
                    'updated_at': current_time
                })
                # 添加更新时间戳HTTP头
                response['X-Updated-At'] = str(current_time)
                return response
            else:
                logger.warning(f"回购记录更新数据验证失败 - 错误: {serializer.errors}")
                return Response({
                    'error': '数据验证失败',
                    'details': serializer.errors
                }, status=status.HTTP_400_BAD_REQUEST)

        elif request.method == 'DELETE':
            # 判断是否为最新记录
            is_latest = bean.occurrences.order_by('-created_at').first().id == occurrence.id

            # 删除回购记录
            occurrence.delete()

            # 如果删除的是最新记录，需要更新咖啡豆信息
            if is_latest:
                # 如果还有其他回购记录，更新咖啡豆信息为次新记录
                latest_occurrence = bean.occurrences.order_by('-created_at').first()
                if latest_occurrence:
                    bean.bag_weight = latest_occurrence.bag_weight
                    bean.bag_remain = latest_occurrence.bag_remain
                    bean.purchase_price = latest_occurrence.purchase_price
                    bean.roast_date = latest_occurrence.roast_date
                    bean.created_at = latest_occurrence.created_at
                    bean.rest_period_min = latest_occurrence.rest_period_min
                    bean.rest_period_max = latest_occurrence.rest_period_max
                else:
                    # 如果没有回购记录了，恢复到初始值
                    logger.info(f"更新后没有回购记录 - Bean ID: {bean.id}，恢复初始值")
                    bean.bag_weight = bean.initial_bag_weight
                    bean.bag_remain = bean.initial_bag_remain
                    bean.purchase_price = bean.initial_purchase_price
                    bean.roast_date = bean.initial_roast_date
                    # 明确检查initial_created_at是否存在
                    if bean.initial_created_at:
                        logger.info(f"恢复initial_created_at: {bean.initial_created_at}")
                        bean.created_at = bean.initial_created_at
                    else:
                        logger.warning(f"initial_created_at为空，保持当前created_at: {bean.created_at}")
                    bean.rest_period_min = bean.initial_rest_period_min
                    bean.rest_period_max = bean.initial_rest_period_max

                bean.save()

            # 记录保存后的状态
            refreshed_bean = CoffeeBean.objects.get(id=bean.id)
            logger.info(f"更新后的状态: created_at={refreshed_bean.created_at}, initial_created_at={refreshed_bean.initial_created_at}")

            # 清除所有与咖啡豆相关的iOS缓存
            invalidate_ios_cache('bean_list', request.user.id)
            invalidate_ios_cache('bean_calendar_data', request.user.id)
            invalidate_ios_cache('hindsight_data', request.user.id)
            logger.info(f"回购记录删除成功，已清除相关缓存 - Occurrence ID: {occurrence_id}")

            # 序列化并返回更新后的咖啡豆数据
            bean_data = IOSCoffeeBeanSerializer(bean).data

            current_time = int(time.time())
            response = Response({
                'success': True,
                'message': '回购记录删除成功',
                'bean': bean_data,
                'occurrence': None,  # 添加occurrence字段，值为None
                'updated_at': current_time
            })
            # 添加更新时间戳HTTP头
            response['X-Updated-At'] = str(current_time)
            return response

    except Exception as e:
        logger.error(f"处理回购记录请求时发生错误: {str(e)}", exc_info=True)
        return Response({
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['DELETE'])
@permission_classes([IsAuthenticated])
@csrf_exempt  # 豁免CSRF验证，使用JWT认证代替
def delete_bean_occurrence(request, occurrence_id):
    """删除咖啡豆回购记录"""
    logger.info(f"处理删除咖啡豆回购记录请求 - Occurrence ID: {occurrence_id}")

    try:
        # 查找回购记录
        occurrence = BeanOccurrence.objects.filter(
            id=occurrence_id,
            coffee_bean__user=request.user
        ).first()

        if not occurrence:
            logger.warning(f"找不到指定的回购记录 - Occurrence ID: {occurrence_id}, 用户: {request.user.username}")
            return Response({
                'error': '找不到指定的回购记录'
            }, status=status.HTTP_404_NOT_FOUND)

        # 获取关联的咖啡豆
        bean = occurrence.coffee_bean

        # 判断是否为最新记录
        is_latest = bean.occurrences.order_by('-created_at').first().id == occurrence.id

        # 删除回购记录
        occurrence.delete()

        # 如果删除的是最新记录，需要更新咖啡豆信息
        if is_latest:
            # 如果还有其他回购记录，更新咖啡豆信息为次新记录
            latest_occurrence = bean.occurrences.order_by('-created_at').first()
            if latest_occurrence:
                bean.bag_weight = latest_occurrence.bag_weight
                bean.bag_remain = latest_occurrence.bag_remain
                bean.purchase_price = latest_occurrence.purchase_price
                bean.roast_date = latest_occurrence.roast_date
                bean.created_at = latest_occurrence.created_at
                bean.rest_period_min = latest_occurrence.rest_period_min
                bean.rest_period_max = latest_occurrence.rest_period_max
            else:
                # 如果没有回购记录了，恢复到初始值
                bean.bag_weight = bean.initial_bag_weight
                bean.bag_remain = bean.initial_bag_remain
                bean.purchase_price = bean.initial_purchase_price
                bean.roast_date = bean.initial_roast_date
                bean.created_at = bean.initial_created_at
                bean.rest_period_min = bean.initial_rest_period_min
                bean.rest_period_max = bean.initial_rest_period_max

            bean.save()

        # 记录保存后的状态
        refreshed_bean = CoffeeBean.objects.get(id=bean.id)
        logger.info(f"更新后的状态: created_at={refreshed_bean.created_at}, initial_created_at={refreshed_bean.initial_created_at}")

        # 清除所有与咖啡豆相关的iOS缓存
        invalidate_ios_cache('bean_list', request.user.id)
        invalidate_ios_cache('bean_calendar_data', request.user.id)
        invalidate_ios_cache('hindsight_data', request.user.id)
        logger.info(f"回购记录删除成功，已清除相关缓存 - Occurrence ID: {occurrence_id}")

        # 序列化并返回更新后的咖啡豆数据
        bean_data = IOSCoffeeBeanSerializer(bean).data

        current_time = int(time.time())
        response = Response({
            'success': True,
            'message': '回购记录删除成功',
            'bean': bean_data,
            'occurrence': None,  # 添加occurrence字段，值为None
            'updated_at': current_time
        })
        # 添加更新时间戳HTTP头
        response['X-Updated-At'] = str(current_time)
        return response

    except Exception as e:
        logger.error(f"删除回购记录时发生错误: {str(e)}", exc_info=True)
        return Response({
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def data_version(request):
    """获取数据版本信息，用于客户端检查是否需要刷新"""
    logger.info(f"用户 {request.user.username} 请求数据版本信息")

    try:
        # 获取用户的相关实体最后更新时间
        data_types = request.query_params.getlist('data_types', ['beans', 'brewing_records', 'equipment'])
        # 是否包含数据
        include_data = request.query_params.get('include_data', 'false').lower() == 'true'

        versions = {}
        included_data = {}

        # 获取各类数据的最后更新时间
        if 'beans' in data_types:
            # 获取咖啡豆的最后更新时间
            latest_bean = CoffeeBean.objects.filter(
                user=request.user,
                is_deleted=False
            ).order_by('-updated_at').first()

            if latest_bean:
                versions['beans'] = int(latest_bean.updated_at.timestamp())
            else:
                versions['beans'] = 0

            # 如果需要包含数据
            if include_data:
                beans = CoffeeBean.objects.filter(
                    user=request.user,
                    is_deleted=False
                )
                included_data['beans'] = IOSCoffeeBeanSerializer(beans, many=True).data

        if 'brewing_records' in data_types:
            # 获取冲煮记录的最后更新时间
            latest_record = BrewingRecord.objects.filter(
                user=request.user
            ).order_by('-updated_at').first()

            if latest_record:
                versions['brewing_records'] = int(latest_record.updated_at.timestamp())
            else:
                versions['brewing_records'] = 0

        if 'equipment' in data_types:
            # 获取设备的最后更新时间
            latest_equipment_created = Equipment.objects.filter(
                user=request.user,
                is_deleted=False
            ).order_by('-created_at').first()

            latest_equipment_updated = None
            if hasattr(Equipment, 'updated_at'):
                latest_equipment_updated = Equipment.objects.filter(
                    user=request.user,
                    is_deleted=False
                ).order_by('-updated_at').first()

            # 获取设备缓存版本号
            from django.core.cache import cache
            cache_key = f'equipment_version:{request.user.id}'
            cached_version = cache.get(cache_key)

            # 默认时间戳
            equipment_version = 0

            # 如果有更新时间字段，获取最新的更新时间
            if latest_equipment_updated:
                equipment_version = int(latest_equipment_updated.updated_at.timestamp())
            # 否则使用创建时间
            elif latest_equipment_created:
                equipment_version = int(latest_equipment_created.created_at.timestamp())

            # 如果缓存中有更新的版本号，使用缓存的版本号
            if cached_version and cached_version > equipment_version:
                equipment_version = cached_version

            # 强制更新设备使用统计缓存，确保iOS端可以获取正确的使用次数和最后使用时间
            equipment_list = Equipment.objects.filter(
                user=request.user,
                is_deleted=False
            )

            # 预热设备缓存，确保usage_count和last_used字段有值
            for equip in equipment_list:
                try:
                    # 预热缓存
                    _ = equip.get_usage_count()
                    _ = equip.get_last_used_datetime()
                except Exception as e:
                    logger.warning(f"预热设备 {equip.id} 缓存时出错: {str(e)}")

            # 清除equipment_list缓存，强制下次请求重新获取数据
            invalidate_ios_cache('equipment_list', request.user.id)

            versions['equipment'] = equipment_version

            # 如果需要包含数据
            if include_data:
                included_data['equipment'] = IOSEquipmentSerializer(equipment_list, many=True).data

        # 构建响应
        response_data = {
            'versions': versions,
            'server_time': int(time.time())
        }

        # 如果需要包含数据
        if include_data and included_data:
            response_data['data'] = included_data

        return Response(response_data)

    except Exception as e:
        logger.error(f"获取数据版本信息失败: {str(e)}", exc_info=True)
        return Response(
            {'error': '获取数据版本信息失败'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['GET', 'PUT', 'DELETE'])
@permission_classes([IsAuthenticated])
@csrf_exempt  # 豁免CSRF验证，使用JWT认证代替
def equipment_detail(request, equipment_id):
    """设备详情、更新和删除"""
    logger.info(f"处理设备请求 - 方法: {request.method}, 设备ID: {equipment_id}")

    try:
        # 检索设备对象
        equipment = Equipment.objects.get(id=equipment_id, user=request.user)

        # 处理GET请求 - 返回设备详情
        if request.method == 'GET':
            serializer = IOSEquipmentSerializer(equipment)
            response_data = serializer.data

            # 如果是小工具组合，添加组件信息
            if equipment.type == 'GADGET_KIT' and hasattr(equipment, 'gadget_components'):
                components = equipment.gadget_components.filter(is_deleted=False)
                if components.exists():
                    response_data['gadget_components'] = IOSEquipmentSerializer(components, many=True).data

            return Response(response_data)

        # 处理DELETE请求 - 软删除设备
        elif request.method == 'DELETE':
            # 软删除设备
            equipment.is_deleted = True
            equipment.deleted_at = timezone.now()
            equipment.save()

            # 清除所有与设备相关的缓存
            from my.cache_utils import invalidate_equipment_cache
            invalidate_equipment_cache(request.user.id, equipment_id)

            # 清除iOS端相关缓存
            invalidate_ios_cache('equipment_list', request.user.id)
            invalidate_ios_cache('brewlog_statistics', request.user.id)
            invalidate_ios_cache('hindsight_data', request.user.id)
            invalidate_ios_cache('heatmap_data', request.user.id)

            # 更新设备数据版本号
            from django.core.cache import cache
            cache_key = f'equipment_version:{request.user.id}'
            current_timestamp = int(time.time())
            cache.set(cache_key, current_timestamp, timeout=None)  # 无超时时间

            logger.info(f"设备删除成功，已清除相关缓存 - 设备ID: {equipment_id}, 名称: {equipment.name}")

            return Response({
                'success': True,
                'message': '设备删除成功',
                'updated_at': current_timestamp,
                'data_version': current_timestamp  # 添加版本号到响应中
            })

        # 处理不支持的请求方法
        return Response({"error": "不支持的请求方法"}, status=status.HTTP_405_METHOD_NOT_ALLOWED)

    except Equipment.DoesNotExist:
        return Response({"error": "设备不存在或您无权访问"}, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        logger.error(f"处理设备请求时发生错误: {str(e)}", exc_info=True)
        return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['POST'])
@permission_classes([IsAuthenticated])
@csrf_exempt  # 豁免CSRF验证，使用JWT认证代替
def toggle_favorite_equipment(request, equipment_id):
    """切换设备的收藏状态"""
    logger.info(f"开始处理切换设备收藏状态请求 - 设备ID: {equipment_id}")
    try:
        # 查找设备
        equipment = Equipment.objects.filter(
            id=equipment_id,
            user=request.user,
            is_deleted=False
        ).first()

        if not equipment:
            logger.warning(f"找不到指定的设备 - 设备ID: {equipment_id}, 用户: {request.user.username}")
            return Response({
                'error': '找不到指定的设备'
            }, status=status.HTTP_404_NOT_FOUND)

        # 是否为 GADGET_KIT 类型
        is_gadget_kit = equipment.type == 'GADGET_KIT'

        # 如果是 GADGET_KIT，先保存组件引用
        gadget_components = None
        if is_gadget_kit:
            gadget_components = list(equipment.gadget_components.all())
            logger.info(f"GADGET_KIT组件数量: {len(gadget_components)}")

        # 切换收藏状态逻辑
        if equipment.is_favorite:
            # 如果当前已是收藏状态，则取消收藏
            equipment.is_favorite = False
            equipment.save()
            status_str = "取消收藏"
            logger.info(f"成功{status_str}设备 - 设备ID: {equipment_id}, 名称: {equipment.name}")
        else:
            # 如果当前未收藏，则先取消同类型其他收藏的设备
            Equipment.objects.filter(
                user=request.user,
                type=equipment.type,
                is_favorite=True
            ).update(is_favorite=False)

            # 然后设置当前设备为收藏
            equipment.is_favorite = True
            equipment.save()
            status_str = "收藏"
            logger.info(f"成功{status_str}设备 - 设备ID: {equipment_id}, 名称: {equipment.name}")

            # 如果是 GADGET_KIT，确保组件关联不丢失
            if is_gadget_kit and gadget_components:
                # 重新查询设备以确保获取最新状态
                equipment = Equipment.objects.get(id=equipment_id)

                # 检查组件是否丢失
                current_components = list(equipment.gadget_components.all())
                if len(current_components) != len(gadget_components):
                    logger.warning(f"GADGET_KIT组件丢失，执行恢复 - 原有: {len(gadget_components)}, 当前: {len(current_components)}")

                    # 清空并重新添加组件
                    equipment.gadget_components.clear()
                    equipment.gadget_components.add(*gadget_components)
                    logger.info(f"GADGET_KIT组件已恢复 - 设备ID: {equipment_id}, 组件数量: {len(gadget_components)}")

        # 清除所有与设备相关的iOS缓存
        invalidate_ios_cache('equipment_list', request.user.id)
        logger.info(f"已清除用户 {request.user.id} 的设备相关缓存")

        # 更新设备数据版本号
        from django.core.cache import cache
        cache_key = f'equipment_version:{request.user.id}'
        current_timestamp = int(time.time())
        cache.set(cache_key, current_timestamp, timeout=None)  # 无超时时间

        # 构建响应
        return Response({
            'success': True,
            'message': f'已{status_str}设备',
            'is_favorite': equipment.is_favorite,
            'updated_at': current_timestamp,
            'data_version': current_timestamp
        })

    except Exception as e:
        logger.error(f"切换设备收藏状态时发生错误: {str(e)}", exc_info=True)
        return Response({
            'error': f'操作失败: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['POST'])
@permission_classes([IsAuthenticated])
@csrf_exempt  # 豁免CSRF验证，使用JWT认证代替
def archive_equipment(request, equipment_id):
    """归档设备"""
    logger.info(f"开始处理归档设备请求 - 设备ID: {equipment_id}")
    try:
        # 查找设备
        equipment = Equipment.objects.filter(
            id=equipment_id,
            user=request.user,
            is_deleted=False
        ).first()

        if not equipment:
            logger.warning(f"找不到指定的设备 - 设备ID: {equipment_id}, 用户: {request.user.username}")
            return Response({
                'error': '找不到指定的设备'
            }, status=status.HTTP_404_NOT_FOUND)

        # 如果是小工具组合，保存组件关系以备恢复
        gadget_components = None
        if equipment.type == 'GADGET_KIT':
            gadget_components = list(equipment.gadget_components.all())
            logger.info(f"归档小工具组合，已保存组件关系 - 组件数量: {len(gadget_components)}")

        # 归档设备，并自动取消首选状态
        equipment.is_archived = True
        if equipment.is_favorite:
            equipment.is_favorite = False
        equipment.save()

        # 检查小工具组合归档后是否组件丢失，如果丢失则恢复
        if equipment.type == 'GADGET_KIT' and gadget_components:
            # 重新获取最新状态
            equipment = Equipment.objects.get(id=equipment_id)
            current_components = list(equipment.gadget_components.all())

            # 如果组件数量不一致，表示关系丢失，需要恢复
            if len(current_components) != len(gadget_components) and gadget_components:
                logger.warning(f"小工具组合归档后组件关系丢失，执行恢复 - 原有: {len(gadget_components)}, 当前: {len(current_components)}")
                # 清空并重建组件关系
                equipment.gadget_components.clear()
                equipment.gadget_components.add(*gadget_components)
                logger.info(f"小工具组合组件关系已恢复 - 设备ID: {equipment_id}")

        # 清除所有与设备相关的iOS缓存
        invalidate_ios_cache('equipment_list', request.user.id)
        invalidate_ios_cache('brewlog_statistics', request.user.id)
        invalidate_ios_cache('hindsight_data', request.user.id)

        # 更新设备数据版本号
        from django.core.cache import cache
        cache_key = f'equipment_version:{request.user.id}'
        current_timestamp = int(time.time())
        cache.set(cache_key, current_timestamp, timeout=None)  # 无超时时间

        logger.info(f"成功归档设备并清除相关缓存 - 设备ID: {equipment_id}, 名称: {equipment.name}")

        return Response({
            'success': True,
            'message': '已归档设备',
            'is_archived': True,
            'updated_at': current_timestamp,
            'data_version': current_timestamp
        })

    except Exception as e:
        logger.error(f"归档设备时发生错误: {str(e)}", exc_info=True)
        return Response({
            'error': f'操作失败: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['POST'])
@permission_classes([IsAuthenticated])
@csrf_exempt  # 豁免CSRF验证，使用JWT认证代替
def unarchive_equipment(request, equipment_id):
    """取消归档设备"""
    logger.info(f"开始处理取消归档设备请求 - 设备ID: {equipment_id}")
    try:
        # 查找设备
        equipment = Equipment.objects.filter(
            id=equipment_id,
            user=request.user,
            is_deleted=False
        ).first()

        if not equipment:
            logger.warning(f"找不到指定的设备 - 设备ID: {equipment_id}, 用户: {request.user.username}")
            return Response({
                'error': '找不到指定的设备'
            }, status=status.HTTP_404_NOT_FOUND)

        # 如果是小工具组合，保存组件关系以备恢复
        gadget_components = None
        if equipment.type == 'GADGET_KIT':
            gadget_components = list(equipment.gadget_components.all())
            logger.info(f"取消归档小工具组合，已保存组件关系 - 组件数量: {len(gadget_components)}")

        # 取消归档设备
        equipment.is_archived = False
        equipment.save()

        # 检查小工具组合取消归档后是否组件丢失，如果丢失则恢复
        if equipment.type == 'GADGET_KIT' and gadget_components:
            # 重新获取最新状态
            equipment = Equipment.objects.get(id=equipment_id)
            current_components = list(equipment.gadget_components.all())

            # 如果组件数量不一致，表示关系丢失，需要恢复
            if len(current_components) != len(gadget_components) and gadget_components:
                logger.warning(f"小工具组合取消归档后组件关系丢失，执行恢复 - 原有: {len(gadget_components)}, 当前: {len(current_components)}")
                # 清空并重建组件关系
                equipment.gadget_components.clear()
                equipment.gadget_components.add(*gadget_components)
                logger.info(f"小工具组合组件关系已恢复 - 设备ID: {equipment_id}")

        # 清除所有与设备相关的iOS缓存
        invalidate_ios_cache('equipment_list', request.user.id)
        invalidate_ios_cache('brewlog_statistics', request.user.id)
        invalidate_ios_cache('hindsight_data', request.user.id)

        # 更新设备数据版本号
        from django.core.cache import cache
        cache_key = f'equipment_version:{request.user.id}'
        current_timestamp = int(time.time())
        cache.set(cache_key, current_timestamp, timeout=None)  # 无超时时间

        logger.info(f"成功取消归档设备并清除相关缓存 - 设备ID: {equipment_id}, 名称: {equipment.name}")

        return Response({
            'success': True,
            'message': '已取消归档设备',
            'is_archived': False,
            'updated_at': current_timestamp,
            'data_version': current_timestamp
        })

    except Exception as e:
        logger.error(f"取消归档设备时发生错误: {str(e)}", exc_info=True)
        return Response({
            'error': f'操作失败: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def flavor_tags_list(request):
    """
    获取用户的风味标签列表
    """
    tags = FlavorTag.objects.filter(user=request.user).order_by('name')

    # 计算每个标签的使用次数
    result = []
    for tag in tags:
        # 统计风味标签在咖啡豆中的使用次数
        beans_count = tag.coffee_beans.count()

        # 统计风味标签在冲煮记录中的使用次数
        records_count = BrewingRecord.objects.filter(
            user=request.user,
            flavor_tags=tag
        ).count()

        # 总使用次数
        usage_count = beans_count + records_count

        result.append({
            'id': tag.id,
            'name': tag.name,
            'usage_count': usage_count
        })

    return Response(result)

@api_view(['POST'])
@permission_classes([IsAuthenticated])
@csrf_exempt
def create_flavor_tag(request):
    """
    创建新的风味标签
    """
    try:
        tag_name = request.data.get('name', '').strip()

        if not tag_name:
            return Response({'error': '标签名称不能为空'}, status=400)

        # 检查当前用户是否已有相同标签（不区分大小写）
        existing_tag = FlavorTag.objects.filter(
            user=request.user,
            name__iexact=tag_name
        ).first()

        if existing_tag:
            return Response({
                'id': existing_tag.id,
                'name': existing_tag.name,
                'usage_count': 0
            })

        # 创建新标签，关联到当前用户
        new_tag = FlavorTag.objects.create(
            name=tag_name,
            user=request.user
        )
        return Response({
            'id': new_tag.id,
            'name': new_tag.name,
            'usage_count': 0
        })

    except Exception as e:
        return Response({'error': str(e)}, status=500)

@api_view(['POST'])
@permission_classes([IsAuthenticated])
@csrf_exempt  # 豁免CSRF验证，使用JWT认证代替
def create_equipment(request):
    """
    创建新的设备
    """
    try:
        data = request.data
        logger.info(f"创建设备请求 - 数据: {ensure_serializable(data)}")

        # 详细日志记录
        logger.info(f"请求Content-Type: {request.content_type}")
        logger.info(f"请求方法: {request.method}")

        # 检查请求头
        headers = dict(request.headers.items())
        safe_headers = {k: v for k, v in headers.items() if not k.lower() in ['authorization', 'cookie']}
        logger.info(f"请求头: {safe_headers}")

        # 必需的字段
        required_fields = ['name', 'type']
        for field in required_fields:
            if field not in data or not data[field]:
                return Response({'error': f'缺少必需字段: {field}'}, status=400)

        # 创建设备对象
        equipment = Equipment(
            user=request.user,
            name=data['name'],
            type=data['type'],
            brand=data.get('brand', ''),
            notes=data.get('notes', ''),
            is_archived=data.get('is_archived', False),
            is_favorite=data.get('is_favorite', False)
        )

        # 根据设备类型处理特定字段
        if equipment.type == 'BREWER':
            equipment.brew_method = data.get('brew_method', 'POUR_OVER')
        elif equipment.type == 'GRINDER':
            equipment.grind_size_preset = data.get('grind_size_preset', '')
            equipment.grinder_purpose = data.get('grinder_purpose', 'ALL_PURPOSE')

        # 处理购买价格
        if 'purchase_price' in data and data['purchase_price'] is not None:
            try:
                # 确保购买价格是Decimal类型，避免float和Decimal的运算冲突
                if isinstance(data['purchase_price'], (int, float)):
                    equipment.purchase_price = Decimal(str(data['purchase_price']))
                else:
                    equipment.purchase_price = Decimal(str(data['purchase_price']))
            except (ValueError, TypeError, decimal.InvalidOperation) as e:
                logger.warning(f"处理purchase_price失败: {e}, 值: {data['purchase_price']}")
                equipment.purchase_price = None

        # 处理创建时间
        if 'created_at' in data and data['created_at']:
            try:
                # 尝试解析ISO8601格式
                from django.utils.dateparse import parse_datetime
                equipment.created_at = parse_datetime(data['created_at'])
                if not equipment.created_at:
                    # 如果解析失败，使用当前时间
                    equipment.created_at = timezone.now()
            except Exception as e:
                logger.warning(f"解析创建时间失败: {e}")
                equipment.created_at = timezone.now()
        else:
            equipment.created_at = timezone.now()

        # 保存设备
        equipment.save()
        logger.info(f"成功创建设备基本信息 - ID: {equipment.id}, 名称: {equipment.name}")

        # 处理小工具组合内容
        if equipment.type == 'GADGET_KIT' and 'gadget_components' in data and data['gadget_components']:
            components = data['gadget_components']
            logger.info(f"处理小工具组合内容: {components}")

            if isinstance(components, (list, tuple)):
                try:
                    # 获取并添加组件
                    for component_id in components:
                        try:
                            component = Equipment.objects.get(
                                id=component_id,
                                user=request.user,
                                type='GADGET',
                                is_deleted=False
                            )
                            equipment.gadget_components.add(component)
                            logger.info(f"添加小工具组件: {component.name} (ID: {component.id})")
                        except Equipment.DoesNotExist:
                            logger.warning(f"找不到小工具组件 ID: {component_id}")
                except Exception as e:
                    logger.error(f"处理小工具组件时发生错误: {str(e)}")

        # 清除设备缓存
        invalidate_ios_cache('equipment_list', request.user.id)

        # 更新设备数据版本号
        from django.core.cache import cache
        cache_key = f'equipment_version:{request.user.id}'
        current_timestamp = int(time.time())
        cache.set(cache_key, current_timestamp, timeout=None)  # 无超时时间

        # 返回序列化的设备数据
        serializer = IOSEquipmentSerializer(equipment)
        return Response(serializer.data, status=status.HTTP_201_CREATED)

    except Exception as e:
        logger.error(f"创建设备时发生错误: {str(e)}", exc_info=True)
        return Response({
            'error': f'创建设备失败: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['POST'])
@permission_classes([IsAuthenticated])
@csrf_exempt  # 豁免CSRF验证，使用JWT认证代替
def update_equipment(request, equipment_id):
    """更新设备信息"""
    logger.info(f"处理更新设备请求 - 设备ID: {equipment_id}")

    try:
        # 检索设备对象
        equipment = Equipment.objects.get(id=equipment_id, user=request.user)

        # 预处理请求数据
        data = request.data.copy()
        logger.info(f"更新设备请求 - 设备ID: {equipment_id}, 数据: {ensure_serializable(data)}")

        # 更新设备信息
        serializer = IOSEquipmentSerializer(equipment, data=data, partial=True)
        if serializer.is_valid():
            # 特殊处理created_at_timestamp
            if 'created_at_timestamp' in serializer.validated_data:
                timestamp = serializer.validated_data.pop('created_at_timestamp')
                if timestamp is not None:
                    try:
                        # 将时间戳转换为日期时间
                        from datetime import datetime
                        equipment.created_at = datetime.fromtimestamp(float(timestamp))
                        logger.info(f"手动更新created_at字段: {equipment.created_at}")
                    except Exception as e:
                        logger.warning(f"时间戳转换失败: {timestamp}, 错误: {str(e)}")
                        # 失败时不修改字段

            serializer.save()

            # 手动处理特定字段
            if 'purchase_price' in data and data['purchase_price'] is not None:
                try:
                    equipment.purchase_price = Decimal(str(data['purchase_price']))
                    logger.info(f"手动更新purchase_price字段: {equipment.purchase_price}")
                except (ValueError, TypeError, decimal.InvalidOperation) as e:
                    logger.warning(f"处理purchase_price失败: {e}, 值: {data['purchase_price']}")

            # 处理创建时间
            if 'created_at' in data and data['created_at']:
                try:
                    # 尝试解析ISO8601格式
                    from django.utils.dateparse import parse_datetime
                    created_at = parse_datetime(data['created_at'])
                    if created_at:
                        equipment.created_at = created_at
                        logger.info(f"手动更新created_at字段: {equipment.created_at}")
                except Exception as e:
                    logger.warning(f"解析创建时间失败: {e}")

            # 如果是小工具组合，处理组件
            if equipment.type == 'GADGET_KIT' and 'gadget_components' in data and data['gadget_components']:
                # 清除现有组件
                equipment.gadget_components.clear()

                components = data['gadget_components']
                logger.info(f"处理小工具组合内容: {components}")

                if isinstance(components, (list, tuple)):
                    try:
                        # 获取并添加组件
                        for component_id in components:
                            try:
                                component = Equipment.objects.get(
                                    id=component_id,
                                    user=request.user,
                                    type='GADGET',
                                    is_deleted=False
                                )
                                equipment.gadget_components.add(component)
                                logger.info(f"添加小工具组件: {component.name} (ID: {component.id})")
                            except Equipment.DoesNotExist:
                                logger.warning(f"找不到小工具组件 ID: {component_id}")
                    except Exception as e:
                        logger.error(f"处理小工具组件时发生错误: {str(e)}")

            # 保存设备
            equipment.save()

            # 清除所有与设备相关的缓存
            from my.cache_utils import invalidate_equipment_cache
            invalidate_equipment_cache(request.user.id, equipment_id)

            # 清除iOS端相关缓存
            invalidate_ios_cache('equipment_list', request.user.id)
            invalidate_ios_cache('brewlog_statistics', request.user.id)
            invalidate_ios_cache('hindsight_data', request.user.id)

            # 更新设备数据版本号
            from django.core.cache import cache
            cache_key = f'equipment_version:{request.user.id}'
            current_timestamp = int(time.time())
            cache.set(cache_key, current_timestamp, timeout=None)  # 无超时时间

            logger.info(f"设备更新成功，已清除相关缓存 - 设备ID: {equipment_id}, 名称: {equipment.name}")

            return Response({
                'success': True,
                'message': '设备更新成功',
                'data': serializer.data,
                'updated_at': current_timestamp,
                'data_version': current_timestamp
            })
        else:
            logger.warning(f"设备数据验证失败 - 错误: {serializer.errors}")
            return Response({
                'error': '数据验证失败',
                'details': serializer.errors
            }, status=status.HTTP_400_BAD_REQUEST)

    except Equipment.DoesNotExist:
        return Response({"error": "设备不存在或您无权访问"}, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        logger.error(f"处理设备更新请求时发生错误: {str(e)}", exc_info=True)
        return Response({"error": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    """
    创建新的设备的API端点
    注意: 不要在此函数内调用其他视图函数，而是直接实现创建逻辑
    """
    try:
        data = request.data
        logger.info(f"创建设备请求 - 数据: {ensure_serializable(data)}")

        # 详细日志记录
        logger.info(f"请求Content-Type: {request.content_type}")
        logger.info(f"请求方法: {request.method}")

        # 检查请求头
        headers = dict(request.headers.items())
        safe_headers = {k: v for k, v in headers.items() if not k.lower() in ['authorization', 'cookie']}
        logger.info(f"请求头: {safe_headers}")

        # 必需的字段
        required_fields = ['name', 'type']
        for field in required_fields:
            if field not in data or not data[field]:
                return Response({'error': f'缺少必需字段: {field}'}, status=400)

        # 创建设备对象
        equipment = Equipment(
            user=request.user,
            name=data['name'],
            type=data['type'],
            brand=data.get('brand', ''),
            notes=data.get('notes', ''),
            is_archived=data.get('is_archived', False),
            is_favorite=data.get('is_favorite', False)
        )

        # 根据设备类型处理特定字段
        if equipment.type == 'BREWER':
            equipment.brew_method = data.get('brew_method', 'POUR_OVER')
        elif equipment.type == 'GRINDER':
            equipment.grind_size_preset = data.get('grind_size_preset', '')
            equipment.grinder_purpose = data.get('grinder_purpose', 'ALL_PURPOSE')

        # 处理购买价格
        if 'purchase_price' in data and data['purchase_price'] is not None:
            try:
                # 确保购买价格是Decimal类型，避免float和Decimal的运算冲突
                if isinstance(data['purchase_price'], (int, float)):
                    equipment.purchase_price = Decimal(str(data['purchase_price']))
                else:
                    equipment.purchase_price = Decimal(str(data['purchase_price']))
            except (ValueError, TypeError, decimal.InvalidOperation) as e:
                logger.warning(f"处理purchase_price失败: {e}, 值: {data['purchase_price']}")
                equipment.purchase_price = None

        # 处理创建时间
        if 'created_at' in data and data['created_at']:
            try:
                # 尝试解析ISO8601格式
                from django.utils.dateparse import parse_datetime
                equipment.created_at = parse_datetime(data['created_at'])
                if not equipment.created_at:
                    # 如果解析失败，使用当前时间
                    equipment.created_at = timezone.now()
            except Exception as e:
                logger.warning(f"解析创建时间失败: {e}")
                equipment.created_at = timezone.now()
        else:
            equipment.created_at = timezone.now()

        # 保存设备
        equipment.save()
        logger.info(f"成功创建设备基本信息 - ID: {equipment.id}, 名称: {equipment.name}")

        # 处理小工具组合内容
        if equipment.type == 'GADGET_KIT' and 'gadget_components' in data and data['gadget_components']:
            components = data['gadget_components']
            logger.info(f"处理小工具组合内容: {components}")

            if isinstance(components, (list, tuple)):
                try:
                    # 获取并添加组件
                    for component_id in components:
                        try:
                            component = Equipment.objects.get(
                                id=component_id,
                                user=request.user,
                                type='GADGET',
                                is_deleted=False
                            )
                            equipment.gadget_components.add(component)
                            logger.info(f"添加小工具组件: {component.name} (ID: {component.id})")
                        except Equipment.DoesNotExist:
                            logger.warning(f"找不到小工具组件 ID: {component_id}")
                except Exception as e:
                    logger.error(f"处理小工具组件时发生错误: {str(e)}")

        # 清除设备缓存
        invalidate_ios_cache('equipment_list', request.user.id)

        # 更新设备数据版本号
        from django.core.cache import cache
        cache_key = f'equipment_version:{request.user.id}'
        current_timestamp = int(time.time())
        cache.set(cache_key, current_timestamp, timeout=None)  # 无超时时间

        # 返回序列化的设备数据
        serializer = IOSEquipmentSerializer(equipment)
        return Response(serializer.data, status=status.HTTP_201_CREATED)

    except Exception as e:
        logger.error(f"创建设备时发生错误: {str(e)}", exc_info=True)
        return Response({
            'error': f'创建设备失败: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
@ios_cache_response('gadget_list', timeout=300)  # 5分钟缓存
def gadget_list(request):
    """获取用户的可用小工具列表（未归档、未删除的小工具）以及已选定的小工具"""
    logger.info("开始处理获取小工具列表请求")
    try:
        # 获取请求参数
        equipment_id = request.query_params.get('equipment_id')
        logger.info(f"小工具列表请求 - 设备ID: {equipment_id}")

        # 已经选定的小工具ID集合
        selected_gadget_ids = set()

        # 如果提供了设备ID，获取该设备已选择的小工具
        if equipment_id:
            try:
                equipment = Equipment.objects.get(
                    id=equipment_id,
                    user=request.user,
                    type='GADGET_KIT'
                )
                # 获取所有关联的小工具ID，包括已删除和已归档的
                selected_gadget_ids = set(equipment.gadget_components.all().values_list('id', flat=True))
                logger.info(f"找到 {len(selected_gadget_ids)} 个已选择的小工具")
            except Equipment.DoesNotExist:
                logger.warning(f"找不到指定的小工具组合 - 设备ID: {equipment_id}")

        # 两个查询集：活跃小工具和已选但不活跃的小工具
        active_gadgets = Equipment.objects.filter(
            user=request.user,
            type='GADGET',
            is_archived=False,
            is_deleted=False
        )

        # 如果有已选择的小工具，获取那些已归档或已删除但已被选择的小工具
        selected_inactive_gadgets = Equipment.objects.none()
        if selected_gadget_ids:
            selected_inactive_gadgets = Equipment.objects.filter(
                user=request.user,
                type='GADGET',
                id__in=selected_gadget_ids
            ).exclude(
                is_archived=False,
                is_deleted=False
            )

        # 合并两个查询集并按名称排序
        from django.db.models import Q, Value, BooleanField
        from django.db.models.functions import Concat

        # 为活跃小工具添加is_disabled字段，值为False
        active_gadgets = active_gadgets.annotate(
            is_disabled=Value(False, output_field=BooleanField())
        )

        # 为已选但不活跃的小工具添加is_disabled字段，值为True
        selected_inactive_gadgets = selected_inactive_gadgets.annotate(
            is_disabled=Value(True, output_field=BooleanField())
        )

        # 合并查询集
        all_gadgets = active_gadgets.union(selected_inactive_gadgets).order_by('name')

        # 创建自定义序列化器类，包含is_disabled字段
        class CustomEquipmentSerializer(IOSEquipmentSerializer):
            is_disabled = serializers.BooleanField(default=False)

            class Meta(IOSEquipmentSerializer.Meta):
                fields = IOSEquipmentSerializer.Meta.fields + ['is_disabled']

        # 序列化数据
        serializer = CustomEquipmentSerializer(all_gadgets, many=True)
        data = serializer.data

        logger.info(f"成功获取小工具列表，共 {len(data)} 个小工具")
        return Response(data)

    except Exception as e:
        logger.error(f"处理小工具列表请求时发生错误: {str(e)}", exc_info=True)
        return Response(
            {'error': '获取小工具列表失败'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

# 添加以下用于处理冲煮记录API的视图函数

@api_view(['POST'])
@permission_classes([IsAuthenticated])
@csrf_exempt
def create_brewlog(request):
    """创建冲煮记录"""
    logger.info("处理创建冲煮记录请求")

    # 记录请求头信息（用于调试CSRF问题）
    auth_header = request.META.get('HTTP_AUTHORIZATION', 'None')
    csrf_header = request.META.get('HTTP_X_CSRFTOKEN', 'None')
    content_type = request.META.get('CONTENT_TYPE', 'None')

    if auth_header != 'None':
        auth_header_preview = auth_header[:15] + '...'
    else:
        auth_header_preview = 'None'

    if csrf_header != 'None':
        csrf_header_preview = csrf_header[:10] + '...'
    else:
        csrf_header_preview = 'None'

    logger.debug(f"请求头信息 - Auth: {auth_header_preview}, CSRF: {csrf_header_preview}, Content-Type: {content_type}")

    try:
        # 获取请求数据
        data = request.data

        # 记录请求详情用于调试
        logger.debug(f"创建冲煮记录请求数据: {data}")

        # 确保必要字段存在
        required_fields = ['coffee_bean', 'brewing_equipment']
        for field in required_fields:
            if field not in data:
                logger.warning(f"创建冲煮记录请求缺少必要字段: {field}")
                return Response(
                    {'error': f'缺少必要字段: {field}'},
                    status=status.HTTP_400_BAD_REQUEST
                )

        # 创建冲煮记录
        # 首先检查关联的咖啡豆和设备是否存在
        try:
            coffee_bean = CoffeeBean.objects.get(id=data['coffee_bean'], user=request.user)
        except CoffeeBean.DoesNotExist:
            logger.warning(f"创建冲煮记录失败 - 咖啡豆不存在或不属于当前用户: ID={data['coffee_bean']}")
            return Response(
                {'error': '指定的咖啡豆不存在或不属于当前用户'},
                status=status.HTTP_404_NOT_FOUND
            )

        try:
            brewing_equipment = Equipment.objects.get(id=data['brewing_equipment'], user=request.user)
        except Equipment.DoesNotExist:
            logger.warning(f"创建冲煮记录失败 - 冲煮设备不存在或不属于当前用户: ID={data['brewing_equipment']}")
            return Response(
                {'error': '指定的冲煮设备不存在或不属于当前用户'},
                status=status.HTTP_404_NOT_FOUND
            )

        # 检查研磨设备（如果提供）
        grinding_equipment = None
        if 'grinding_equipment' in data and data['grinding_equipment']:
            try:
                grinding_equipment = Equipment.objects.get(id=data['grinding_equipment'], user=request.user)
            except Equipment.DoesNotExist:
                logger.warning(f"创建冲煮记录失败 - 研磨设备不存在或不属于当前用户: ID={data['grinding_equipment']}")
                return Response(
                    {'error': '指定的研磨设备不存在或不属于当前用户'},
                    status=status.HTTP_404_NOT_FOUND
                )

        # 检查器具套件（如果提供）
        gadget_kit = None
        if 'gadget_kit' in data and data['gadget_kit']:
            try:
                gadget_kit = Equipment.objects.get(id=data['gadget_kit'], user=request.user)
            except Equipment.DoesNotExist:
                logger.warning(f"创建冲煮记录失败 - 器具套件不存在或不属于当前用户: ID={data['gadget_kit']}")
                return Response(
                    {'error': '指定的器具套件不存在或不属于当前用户'},
                    status=status.HTTP_404_NOT_FOUND
                )

        # 创建记录对象
        record = BrewingRecord(
            user=request.user,
            coffee_bean=coffee_bean,
            brewing_equipment=brewing_equipment,
            grinding_equipment=grinding_equipment,
            gadget_kit=gadget_kit
        )

        # 特殊处理brewing_time字段 - 将整数秒数转换为timedelta
        if 'brewing_time' in data:
            try:
                # 检查是否为整数或可转换为整数的字符串
                seconds = int(data['brewing_time'])
                from datetime import timedelta
                # 将秒数转换为timedelta对象
                record.brewing_time = timedelta(seconds=seconds)
                logger.debug(f"将brewing_time从整数 {seconds} 转换为timedelta: {record.brewing_time}")
                # 从data中移除brewing_time，以防后续字段处理时重复设置
                data = {k: v for k, v in data.items() if k != 'brewing_time'}
            except (ValueError, TypeError) as e:
                logger.warning(f"转换brewing_time时出错: {str(e)}，值: {data['brewing_time']}")
                return Response(
                    {'error': f'萃取时间格式无效: {data["brewing_time"]}'},
                    status=status.HTTP_400_BAD_REQUEST
                )

        # 处理created_at字段的设置
        if 'created_at' in data:
            created_at_data = data['created_at']
            if isinstance(created_at_data, (int, float)):
                # 如果是数字类型时间戳，转换为日期时间对象
                from datetime import datetime
                record.created_at = datetime.fromtimestamp(created_at_data)
                logger.info(f"设置记录时间: {record.created_at}")
            elif isinstance(created_at_data, str):
                try:
                    # 尝试ISO格式解析
                    from datetime import datetime
                    record.created_at = datetime.fromisoformat(created_at_data.replace('Z', '+00:00'))
                    logger.info(f"设置记录时间: {record.created_at}")
                except ValueError:
                    logger.warning(f"无法解析created_at字符串: {created_at_data}")
                    # 使用默认时间（当前时间）

        # 设置其他字段
        fields_to_copy = [
            'recipe_name', 'grind_size', 'dose_weight', 'yield_weight',
            'water_temperature', 'rating_level',
            'aroma', 'acidity', 'sweetness', 'body', 'aftertaste',
            'water_quality', 'room_temperature', 'room_humidity', 'notes'
        ]

        for field in fields_to_copy:
            if field in data:
                setattr(record, field, data[field])

        # 保存记录
        record.save()

        # 处理风味标签
        if 'flavor_tags' in data and data['flavor_tags']:
            # 首先清除所有标签
            record.flavor_tags.clear()
            # 然后添加新标签
            for tag_id in data['flavor_tags']:
                try:
                    tag = FlavorTag.objects.get(id=tag_id)
                    record.flavor_tags.add(tag)
                except FlavorTag.DoesNotExist:
                    logger.warning(f"风味标签不存在: ID={tag_id}")
                    # 不因为标签不存在而中断整个流程

        # 处理器具
        if 'gadgets' in data and data['gadgets']:
            # 首先清除所有器具
            record.gadgets.clear()
            # 然后添加新器具
            for gadget_id in data['gadgets']:
                try:
                    gadget = Equipment.objects.get(id=gadget_id, user=request.user)
                    record.gadgets.add(gadget)
                except Equipment.DoesNotExist:
                    logger.warning(f"器具不存在或不属于当前用户: ID={gadget_id}")
                    # 不因为器具不存在而中断整个流程

        # 处理冲煮步骤
        if 'steps' in data and data['steps']:
            # 冲煮步骤现在直接存储在BrewingRecord的steps字段中
            # 这是一个JSONField，不需要创建单独的BrewingStep对象
            record.steps = data['steps']
            record.save(update_fields=['steps'])
            logger.info(f"已保存冲煮步骤，共 {len(data['steps'])} 个步骤")

        # 更新咖啡豆库存
        if coffee_bean and not coffee_bean.is_deleted and not coffee_bean.is_archived:
            try:
                # 如果有库存记录，则减少库存
                if coffee_bean.bag_remain is not None:
                    # 计算新的库存值
                    new_bag_remain = coffee_bean.bag_remain - record.dose_weight

                    # 确保不小于0
                    if new_bag_remain < 0:
                        new_bag_remain = 0

                    # 更新库存
                    coffee_bean.bag_remain = new_bag_remain
                    coffee_bean.save()

                    # 记录日志
                    logger.info(f"创建冲煮记录 ID={record.id}：已更新咖啡豆 {coffee_bean.id} 的库存，减少 {record.dose_weight}g，新库存为 {new_bag_remain}g")
            except Exception as e:
                # 库存更新失败不应影响记录创建
                logger.error(f"更新咖啡豆库存时出错: {str(e)}")

        # 清除相关缓存
        try:
            # 使用my.cache_utils中的函数清除缓存
            from my.cache_utils import (
                invalidate_bean_cache,
                invalidate_hindsight_cache,
                invalidate_record_cache
            )
            from iosapp.cache_utils import invalidate_ios_cache

            # 清除咖啡豆相关缓存
            if coffee_bean:
                invalidate_bean_cache(request.user.id, coffee_bean.id)
                logger.info(f"已清除咖啡豆缓存: {coffee_bean.id}")

            # 清除用户统计数据缓存
            invalidate_hindsight_cache(request.user.id)
            logger.info(f"已清除用户统计数据缓存")

            # 清除冲煮记录缓存
            invalidate_record_cache(request.user.id, record.id)
            logger.info(f"已清除冲煮记录缓存: {record.id}")

            # 清除iOS API缓存
            invalidate_ios_cache('brewlog_list', request.user.id)
            invalidate_ios_cache('brewlog_statistics', request.user.id)
            invalidate_ios_cache('hindsight_data', request.user.id)
            invalidate_ios_cache('heatmap_data', request.user.id)
            invalidate_ios_cache('bean_calendar_data', request.user.id)
            invalidate_ios_cache('filtered_brewlog_list', request.user.id)
            invalidate_ios_cache('bean_list', request.user.id)
            logger.info(f"已清除用户 {request.user.id} 的iOS API缓存")

        except Exception as e:
            # 缓存清除失败不应影响记录创建
            logger.error(f"清除缓存时出错: {str(e)}")

        # 序列化并返回创建的记录
        serializer = IOSBrewingRecordSerializer(record)
        logger.info(f"成功创建冲煮记录: ID={record.id}")

        # 更新相关缓存
        try:
            # 清除记录和趋势缓存
            from my.models import get_trend_cache_key
            from my.cache_utils import invalidate_hindsight_cache, invalidate_bean_cache, invalidate_record_cache
            from django.core.cache import cache

            # 使用本模块中的get_trend_cache_key函数
            trend_cache_key = get_trend_cache_key(request.user.id, record.id)
            cache.delete(trend_cache_key)

            # 清除用户统计数据缓存
            invalidate_hindsight_cache(request.user.id)

            # 如果有咖啡豆，清除咖啡豆缓存
            if record.coffee_bean:
                invalidate_bean_cache(request.user.id, record.coffee_bean.id)

            # 清除冲煮记录相关缓存
            invalidate_record_cache(request.user.id, record.id)

            # 清除iOS API缓存
            invalidate_ios_cache('brewlog_list', request.user.id)
            invalidate_ios_cache('brewlog_statistics', request.user.id)
            invalidate_ios_cache('hindsight_data', request.user.id)
            invalidate_ios_cache('heatmap_data', request.user.id)

            logger.info(f"已清除用户 {request.user.id} 相关缓存")
        except Exception as e:
            # 缓存清除失败不应影响记录创建操作
            logger.error(f"清除缓存时出错: {str(e)}")

        return Response(serializer.data, status=status.HTTP_201_CREATED)

    except Exception as e:
        logger.error(f"创建冲煮记录时发生错误: {str(e)}", exc_info=True)
        return Response(
            {'error': f'创建冲煮记录失败: {str(e)}'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_brewlog(request, record_id):
    """获取单个冲煮记录详情"""
    logger.info(f"获取冲煮记录详情: ID={record_id}")
    try:
        # 查询记录
        record = BrewingRecord.objects.filter(
            id=record_id,
            user=request.user
        ).select_related(
            'brewing_equipment',
            'coffee_bean',
            'grinding_equipment',
            'gadget_kit'
        ).prefetch_related(
            'flavor_tags',
            'gadgets'
        ).first()

        if not record:
            logger.warning(f"冲煮记录不存在或不属于当前用户: ID={record_id}")
            return Response(
                {'error': '记录不存在或不属于当前用户'},
                status=status.HTTP_404_NOT_FOUND
            )

        # 序列化并返回数据
        serializer = IOSBrewingRecordSerializer(record)
        data = serializer.data

        # 检查coffee_bean字段是否有id
        if 'coffee_bean' not in data or not isinstance(data['coffee_bean'], dict) or 'id' not in data['coffee_bean']:
            # 如果没有id，添加默认的coffee_bean对象
            data['coffee_bean'] = {
                'id': 0,
                'name': '',
                'roaster': '',
                'origin': '',
                'process': '',
                'type': 'SINGLE',
                'type_display': '单品',
                'roast_level': 3,
                'roast_level_display': '中浅烘',
                'description': '',
                'price': None,
                'weight': None,
                'purchase_date': None,
                'roast_date': None,
                'is_finished': False,
                'created_at': None,
                'is_favorite': False,
                'is_decaf': False,
                'is_archived': False,
                'is_deleted': False,
                'altitude_type': 'SINGLE',
                'altitude_single': None,
                'altitude_min': None,
                'altitude_max': None,
                'region': '',
                'finca': '',
                'variety': '',
                'barcode': '',
                'rest_period_min': None,
                'rest_period_max': None,
                'rest_period_progress': None,
                'stock_status': '充足',
                'deleted_at': None,
                'bag_remain': None,
                'notes': '',
                'taste_notes': []
            }

        logger.info(f"成功获取冲煮记录详情: ID={record_id}")
        return Response(data)

    except Exception as e:
        logger.error(f"获取冲煮记录详情时发生错误: {str(e)}", exc_info=True)
        return Response(
            {'error': '获取冲煮记录详情失败'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['PUT'])
@permission_classes([IsAuthenticated])
@csrf_exempt
def update_brewlog(request, record_id):
    """更新冲煮记录"""
    logger.info(f"更新冲煮记录: ID={record_id}")
    try:
        # 获取请求数据
        data = request.data

        # 记录请求详情用于调试
        logger.debug(f"更新冲煮记录请求数据: {data}")

        # 查询要更新的记录
        record = BrewingRecord.objects.filter(
            id=record_id,
            user=request.user
        ).select_related(
            'brewing_equipment',
            'coffee_bean',
            'grinding_equipment',
            'gadget_kit'
        ).prefetch_related(
            'flavor_tags',
            'gadgets'
        ).first()

        if not record:
            logger.warning(f"冲煮记录不存在或不属于当前用户: ID={record_id}")
            return Response(
                {'error': '记录不存在或不属于当前用户'},
                status=status.HTTP_404_NOT_FOUND
            )

        # 保存原始咖啡豆和剂量信息用于后续库存更新
        original_coffee_bean_id = record.coffee_bean_id
        original_dose_weight = record.dose_weight
        original_coffee_bean = record.coffee_bean

        # 更新关联的咖啡豆（如果提供）
        if 'coffee_bean' in data:
            try:
                coffee_bean = CoffeeBean.objects.get(id=data['coffee_bean'], user=request.user)
                record.coffee_bean = coffee_bean
            except CoffeeBean.DoesNotExist:
                logger.warning(f"更新冲煮记录失败 - 咖啡豆不存在或不属于当前用户: ID={data['coffee_bean']}")
                return Response(
                    {'error': '指定的咖啡豆不存在或不属于当前用户'},
                    status=status.HTTP_404_NOT_FOUND
                )

        # 更新关联的冲煮设备（如果提供）
        if 'brewing_equipment' in data:
            try:
                brewing_equipment = Equipment.objects.get(id=data['brewing_equipment'], user=request.user)
                record.brewing_equipment = brewing_equipment
            except Equipment.DoesNotExist:
                logger.warning(f"更新冲煮记录失败 - 冲煮设备不存在或不属于当前用户: ID={data['brewing_equipment']}")
                return Response(
                    {'error': '指定的冲煮设备不存在或不属于当前用户'},
                    status=status.HTTP_404_NOT_FOUND
                )

        # 更新关联的研磨设备（如果提供）
        if 'grinding_equipment' in data:
            if data['grinding_equipment']:
                try:
                    grinding_equipment = Equipment.objects.get(id=data['grinding_equipment'], user=request.user)
                    record.grinding_equipment = grinding_equipment
                except Equipment.DoesNotExist:
                    logger.warning(f"更新冲煮记录失败 - 研磨设备不存在或不属于当前用户: ID={data['grinding_equipment']}")
                    return Response(
                        {'error': '指定的研磨设备不存在或不属于当前用户'},
                        status=status.HTTP_404_NOT_FOUND
                    )
            else:
                record.grinding_equipment = None

        # 更新关联的器具套件（如果提供）
        if 'gadget_kit' in data:
            if data['gadget_kit']:
                try:
                    gadget_kit = Equipment.objects.get(id=data['gadget_kit'], user=request.user)
                    record.gadget_kit = gadget_kit
                except Equipment.DoesNotExist:
                    logger.warning(f"更新冲煮记录失败 - 器具套件不存在或不属于当前用户: ID={data['gadget_kit']}")
                    return Response(
                        {'error': '指定的器具套件不存在或不属于当前用户'},
                        status=status.HTTP_404_NOT_FOUND
                    )
            else:
                record.gadget_kit = None

        # 特殊处理brewing_time字段 - 将整数秒数转换为timedelta
        if 'brewing_time' in data:
            try:
                # 检查是否为整数或可转换为整数的字符串
                seconds = int(data['brewing_time'])
                from datetime import timedelta
                # 将秒数转换为timedelta对象
                record.brewing_time = timedelta(seconds=seconds)
                logger.debug(f"将brewing_time从整数 {seconds} 转换为timedelta: {record.brewing_time}")
                # 从data中移除brewing_time，以防后续字段处理时重复设置
                data = {k: v for k, v in data.items() if k != 'brewing_time'}
            except (ValueError, TypeError) as e:
                logger.warning(f"转换brewing_time时出错: {str(e)}，值: {data['brewing_time']}")
                return Response(
                    {'error': f'萃取时间格式无效: {data["brewing_time"]}'},
                    status=status.HTTP_400_BAD_REQUEST
                )

        # 处理created_at字段的更新
        if 'created_at' in data:
            created_at_data = data['created_at']
            if isinstance(created_at_data, (int, float)):
                # 如果是数字类型时间戳，转换为日期时间对象
                from datetime import datetime
                record.created_at = datetime.fromtimestamp(created_at_data)
                logger.info(f"更新记录时间: {record.created_at}")
            elif isinstance(created_at_data, str):
                try:
                    # 尝试ISO格式解析
                    from datetime import datetime
                    record.created_at = datetime.fromisoformat(created_at_data.replace('Z', '+00:00'))
                    logger.info(f"更新记录时间: {record.created_at}")
                except ValueError:
                    logger.warning(f"无法解析created_at字符串: {created_at_data}")
                    # 保持原有值

        # 更新其他字段
        fields_to_update = [
            'recipe_name', 'grind_size', 'dose_weight', 'yield_weight',
            'water_temperature', 'rating_level',
            'aroma', 'acidity', 'sweetness', 'body', 'aftertaste',
            'water_quality', 'room_temperature', 'room_humidity', 'notes'
        ]

        for field in fields_to_update:
            if field in data:
                setattr(record, field, data[field])

        # 保存更新后的记录
        record.save()

        # 更新风味标签
        if 'flavor_tags' in data:
            # 清除现有标签
            record.flavor_tags.clear()
            # 添加新标签
            for tag_id in data['flavor_tags']:
                try:
                    tag = FlavorTag.objects.get(id=tag_id)
                    record.flavor_tags.add(tag)
                except FlavorTag.DoesNotExist:
                    logger.warning(f"风味标签不存在: ID={tag_id}")
                    # 不因为标签不存在而中断整个流程

        # 更新器具
        if 'gadgets' in data:
            # 清除现有器具
            record.gadgets.clear()
            # 添加新器具
            for gadget_id in data['gadgets']:
                try:
                    gadget = Equipment.objects.get(id=gadget_id, user=request.user)
                    record.gadgets.add(gadget)
                except Equipment.DoesNotExist:
                    logger.warning(f"器具不存在或不属于当前用户: ID={gadget_id}")
                    # 不因为器具不存在而中断整个流程

        # 更新步骤
        if 'steps' in data:
            # 直接更新record的steps字段，这是一个JSONField
            record.steps = data['steps']
            record.save(update_fields=['steps'])
            logger.info(f"已更新冲煮步骤，共 {len(data['steps'])} 个步骤")

        # 更新咖啡豆库存
        # 如果咖啡豆发生了变化或者剂量发生了变化，需要更新库存
        if original_coffee_bean_id != record.coffee_bean_id or original_dose_weight != record.dose_weight:
            # 如果原始咖啡豆存在且不是已删除或归档状态，恢复其库存
            if original_coffee_bean and not original_coffee_bean.is_deleted and not original_coffee_bean.is_archived:
                try:
                    if original_coffee_bean.bag_remain is not None:
                        # 恢复原咖啡豆库存
                        new_bag_remain = original_coffee_bean.bag_remain + original_dose_weight
                        # 确保不超过总重量
                        if original_coffee_bean.bag_weight and new_bag_remain > original_coffee_bean.bag_weight:
                            new_bag_remain = original_coffee_bean.bag_weight

                        original_coffee_bean.bag_remain = new_bag_remain
                        original_coffee_bean.save()
                        logger.info(f"更新冲煮记录 ID={record.id}：已恢复原咖啡豆 {original_coffee_bean.id} 的库存，增加 {original_dose_weight}g，新库存为 {new_bag_remain}g")
                except Exception as e:
                    logger.error(f"恢复原咖啡豆库存时出错: {str(e)}")

            # 如果新咖啡豆存在且不是已删除或归档状态，减少其库存
            if record.coffee_bean and not record.coffee_bean.is_deleted and not record.coffee_bean.is_archived:
                try:
                    if record.coffee_bean.bag_remain is not None:
                        # 减少新咖啡豆库存
                        new_bag_remain = record.coffee_bean.bag_remain - record.dose_weight
                        # 确保不小于0
                        if new_bag_remain < 0:
                            new_bag_remain = 0

                        record.coffee_bean.bag_remain = new_bag_remain
                        record.coffee_bean.save()
                        logger.info(f"更新冲煮记录 ID={record.id}：已更新新咖啡豆 {record.coffee_bean.id} 的库存，减少 {record.dose_weight}g，新库存为 {new_bag_remain}g")
                except Exception as e:
                    logger.error(f"更新新咖啡豆库存时出错: {str(e)}")

        # 返回更新后的记录
        serializer = IOSBrewingRecordSerializer(record)
        logger.info(f"成功更新冲煮记录: ID={record.id}")

        # 更新相关缓存
        try:
            # 清除记录和趋势缓存
            from my.models import get_trend_cache_key
            from my.cache_utils import invalidate_hindsight_cache, invalidate_bean_cache, invalidate_record_cache
            from django.core.cache import cache

            # 使用本模块中的get_trend_cache_key函数
            trend_cache_key = get_trend_cache_key(request.user.id, record.id)
            cache.delete(trend_cache_key)

            # 清除用户统计数据缓存
            invalidate_hindsight_cache(request.user.id)

            # 如果有咖啡豆，清除咖啡豆缓存
            if record.coffee_bean:
                invalidate_bean_cache(request.user.id, record.coffee_bean.id)

            # 清除冲煮记录相关缓存
            invalidate_record_cache(request.user.id, record.id)

            # 清除iOS API缓存
            invalidate_ios_cache('brewlog_list', request.user.id)
            invalidate_ios_cache('brewlog_statistics', request.user.id)
            invalidate_ios_cache('hindsight_data', request.user.id)
            invalidate_ios_cache('heatmap_data', request.user.id)

            logger.info(f"已清除用户 {request.user.id} 相关缓存")
        except Exception as e:
            # 缓存清除失败不应影响记录更新操作
            logger.error(f"清除缓存时出错: {str(e)}")

        return Response(serializer.data)

    except Exception as e:
        logger.error(f"更新冲煮记录时发生错误: {str(e)}", exc_info=True)
        return Response(
            {'error': f'更新冲煮记录失败: {str(e)}'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['GET'])
@permission_classes([IsAuthenticated])
@ios_cache_response('available_years', timeout=3600)  # 1小时缓存
def available_years(request):
    """获取有冲煮记录的年份列表"""
    try:
        logger.info("获取可用年份列表")
        years_data = BrewingRecord.objects.get_available_years(request.user)

        # 确保返回的数据包含正确的字段
        if not isinstance(years_data, dict):
            years_data = {'years': years_data, 'current_year': timezone.now().year}
        elif 'years' not in years_data:
            years_data['years'] = [timezone.now().year]
        elif 'current_year' not in years_data:
            years_data['current_year'] = timezone.now().year

        # 确保最新的年份总是在列表中
        current_year = timezone.now().year
        if current_year not in years_data['years']:
            years_data['years'].append(current_year)
            years_data['years'].sort(reverse=True)  # 降序排列

        logger.info(f"返回年份列表: {years_data}")
        return Response(years_data)
    except Exception as e:
        logger.error("获取可用年份列表错误: %s", str(e), exc_info=True)
        return Response(
            {'error': str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


# 配方相关API
@api_view(['GET'])
@permission_classes([IsAuthenticated])
@ios_cache_response('recipe_list', timeout=1800)  # 30分钟缓存
def recipe_list(request):
    """获取用户的配方列表"""
    try:
        logger.info(f"获取用户 {request.user.id} 的配方列表")

        # 获取筛选参数
        tag_id = request.query_params.get('tag')
        sort_by = request.query_params.get('sort_by', 'recent')  # 默认按最近使用排序

        # 获取所有配方主题
        from my.models import RecipeThread, RecipeTag
        recipes = RecipeThread.get_user_recipes(request.user)

        # 标签筛选
        if tag_id:
            try:
                tag = RecipeTag.objects.get(id=tag_id, user=request.user)
                recipes = [r for r in recipes if tag in r.tags.all()]
            except RecipeTag.DoesNotExist:
                pass

        # 排序
        if sort_by == 'count':
            recipes = sorted(recipes, key=lambda x: (-x.use_count, x.recipe_name))
        else:  # recent
            recipes = sorted(recipes, key=lambda x: (x.last_used_at or x.created_at).timestamp(), reverse=True)

        # 序列化数据
        from .serializers import IOSRecipeThreadSerializer
        serializer = IOSRecipeThreadSerializer(recipes, many=True)

        logger.info(f"成功获取配方列表，共 {len(recipes)} 个配方")
        return Response(serializer.data)

    except Exception as e:
        logger.error(f"获取配方列表时发生错误: {str(e)}", exc_info=True)
        return Response(
            {'error': '获取配方列表失败'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['GET', 'POST'])
@permission_classes([IsAuthenticated])
@csrf_exempt
def recipe_tags_list(request):
    """配方标签管理 - 获取标签列表或创建新标签"""

    if request.method == 'GET':
        # 获取用户的配方标签列表
        try:
            logger.info(f"获取用户 {request.user.id} 的配方标签列表")

            from my.models import RecipeTag
            tags = RecipeTag.objects.filter(user=request.user).order_by('name')

            # 序列化数据
            from .serializers import IOSRecipeTagSerializer
            serializer = IOSRecipeTagSerializer(tags, many=True)

            logger.info(f"成功获取配方标签列表，共 {len(tags)} 个标签")
            return Response(serializer.data)

        except Exception as e:
            logger.error(f"获取配方标签列表时发生错误: {str(e)}", exc_info=True)
            return Response(
                {'error': '获取配方标签列表失败'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    elif request.method == 'POST':
        # 创建新的配方标签
        try:
            logger.info(f"用户 {request.user.id} 创建新的配方标签")

            from my.models import RecipeTag
            tag_name = request.data.get('name', '').strip()

            if not tag_name:
                return Response({
                    'status': 'error',
                    'message': '标签名称不能为空'
                }, status=status.HTTP_400_BAD_REQUEST)

            # 检查当前用户是否已有相同标签（不区分大小写）
            existing_tag = RecipeTag.objects.filter(
                user=request.user,
                name__iexact=tag_name
            ).first()

            if existing_tag:
                # 如果标签已存在，返回现有标签
                from .serializers import IOSRecipeTagSerializer
                serializer = IOSRecipeTagSerializer(existing_tag)
                return Response({
                    'status': 'success',
                    'message': '标签已存在',
                    'tag': serializer.data
                })

            # 创建新标签
            new_tag = RecipeTag.objects.create(
                name=tag_name,
                user=request.user
            )

            # 清除相关缓存
            invalidate_ios_cache('recipe_list', request.user.id)

            # 清除服务端配方缓存
            try:
                from my.cache_utils import invalidate_recipe_cache
                invalidate_recipe_cache(request.user.id)
                logger.info(f"已清除用户 {request.user.id} 的服务端配方缓存")
            except ImportError:
                logger.warning("无法导入服务端缓存模块，跳过服务端缓存清除")

            # 序列化返回新标签
            from .serializers import IOSRecipeTagSerializer
            serializer = IOSRecipeTagSerializer(new_tag)

            logger.info(f"成功创建配方标签: {tag_name}")
            return Response({
                'status': 'success',
                'message': '标签创建成功',
                'tag': serializer.data
            })

        except Exception as e:
            logger.error(f"创建配方标签时发生错误: {str(e)}", exc_info=True)
            return Response({
                'status': 'error',
                'message': f'创建标签失败: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['DELETE', 'PUT', 'PATCH'])
@permission_classes([IsAuthenticated])
@csrf_exempt
def recipe_tag_detail(request, tag_id):
    """配方标签详情管理 - 删除或更新标签"""

    if request.method == 'DELETE':
        # 删除配方标签
        try:
            logger.info(f"用户 {request.user.id} 删除配方标签 {tag_id}")

            from my.models import RecipeTag
            tag = RecipeTag.objects.get(id=tag_id, user=request.user)
            tag_name = tag.name

            # 删除标签
            tag.delete()

            # 清除相关缓存
            invalidate_ios_cache('recipe_list', request.user.id)

            # 清除服务端配方缓存
            try:
                from my.cache_utils import invalidate_recipe_cache
                invalidate_recipe_cache(request.user.id)
                logger.info(f"已清除用户 {request.user.id} 的服务端配方缓存")
            except ImportError:
                logger.warning("无法导入服务端缓存模块，跳过服务端缓存清除")

            logger.info(f"成功删除配方标签: {tag_name}")
            return Response({
                'status': 'success',
                'message': '标签删除成功'
            })

        except RecipeTag.DoesNotExist:
            return Response({
                'status': 'error',
                'message': '标签不存在'
            }, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            logger.error(f"删除配方标签时发生错误: {str(e)}", exc_info=True)
            return Response({
                'status': 'error',
                'message': f'删除标签失败: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    elif request.method in ['PUT', 'PATCH']:
        # 更新配方标签
        try:
            logger.info(f"用户 {request.user.id} 更新配方标签 {tag_id}")

            from my.models import RecipeTag
            tag = RecipeTag.objects.get(id=tag_id, user=request.user)

            new_name = request.data.get('name', '').strip()
            if not new_name:
                return Response({
                    'status': 'error',
                    'message': '标签名称不能为空'
                }, status=status.HTTP_400_BAD_REQUEST)

            # 检查新名称是否与其他标签冲突（不区分大小写）
            existing_tag = RecipeTag.objects.filter(
                user=request.user,
                name__iexact=new_name
            ).exclude(id=tag_id).first()

            if existing_tag:
                return Response({
                    'status': 'error',
                    'message': '已存在同名标签'
                }, status=status.HTTP_400_BAD_REQUEST)

            # 更新标签名称
            old_name = tag.name
            tag.name = new_name
            tag.save()

            # 清除相关缓存
            invalidate_ios_cache('recipe_list', request.user.id)

            # 清除服务端配方缓存
            try:
                from my.cache_utils import invalidate_recipe_cache
                invalidate_recipe_cache(request.user.id)
                logger.info(f"已清除用户 {request.user.id} 的服务端配方缓存")
            except ImportError:
                logger.warning("无法导入服务端缓存模块，跳过服务端缓存清除")

            # 序列化返回更新后的标签
            from .serializers import IOSRecipeTagSerializer
            serializer = IOSRecipeTagSerializer(tag)

            logger.info(f"成功更新配方标签: {old_name} -> {new_name}")
            return Response({
                'status': 'success',
                'message': '标签更新成功',
                'tag': serializer.data
            })

        except RecipeTag.DoesNotExist:
            return Response({
                'status': 'error',
                'message': '标签不存在'
            }, status=status.HTTP_404_NOT_FOUND)
        except Exception as e:
            logger.error(f"更新配方标签时发生错误: {str(e)}", exc_info=True)
            return Response({
                'status': 'error',
                'message': f'更新标签失败: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
@csrf_exempt
def update_recipe_tags(request, recipe_id):
    """更新配方标签"""
    try:
        logger.info(f"用户 {request.user.id} 更新配方 {recipe_id} 的标签")

        from my.models import RecipeThread, RecipeTag

        # 获取配方
        try:
            recipe = RecipeThread.objects.get(id=recipe_id, user=request.user)
        except RecipeThread.DoesNotExist:
            return Response({
                'status': 'error',
                'message': '配方不存在'
            }, status=status.HTTP_404_NOT_FOUND)

        # 获取标签ID列表
        tag_ids = request.data.get('tag_ids', [])
        if not isinstance(tag_ids, list):
            return Response({
                'status': 'error',
                'message': '标签ID必须是数组格式'
            }, status=status.HTTP_400_BAD_REQUEST)

        # 验证所有标签都属于当前用户
        if tag_ids:
            user_tags = RecipeTag.objects.filter(
                id__in=tag_ids,
                user=request.user
            )
            if user_tags.count() != len(tag_ids):
                return Response({
                    'status': 'error',
                    'message': '包含无效的标签ID'
                }, status=status.HTTP_400_BAD_REQUEST)

        # 更新配方标签
        recipe.tags.clear()  # 清除现有标签
        if tag_ids:
            recipe.tags.set(tag_ids)  # 设置新标签

        # 清除相关缓存
        invalidate_ios_cache('recipe_list', request.user.id)

        # 清除服务端配方缓存
        try:
            from my.cache_utils import invalidate_recipe_cache
            invalidate_recipe_cache(request.user.id)
            logger.info(f"已清除用户 {request.user.id} 的服务端配方缓存")
        except ImportError:
            logger.warning("无法导入服务端缓存模块，跳过服务端缓存清除")

        # 重新获取配方数据以包含更新后的标签
        recipe.refresh_from_db()

        # 序列化返回更新后的配方
        from .serializers import IOSRecipeThreadSerializer
        serializer = IOSRecipeThreadSerializer(recipe)

        logger.info(f"成功更新配方 {recipe_id} 的标签，共 {len(tag_ids)} 个标签")
        return Response({
            'status': 'success',
            'message': '配方标签更新成功',
            'recipe': serializer.data
        })

    except Exception as e:
        logger.error(f"更新配方标签时发生错误: {str(e)}", exc_info=True)
        return Response({
            'status': 'error',
            'message': f'更新配方标签失败: {str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def quick_brew(request, recipe_id):
    """快速创建冲煮记录"""
    try:
        logger.info(f"用户 {request.user.id} 快速创建配方 {recipe_id} 的冲煮记录")

        from my.models import RecipeThread
        recipe = RecipeThread.objects.get(id=recipe_id, user=request.user)
        record = recipe.quick_brew()

        if record:
            # 清除相关缓存 - 速记会影响配方使用统计
            invalidate_ios_cache('brewlog_list', request.user.id)         # 冲煮记录列表缓存
            invalidate_ios_cache('brewlog_statistics', request.user.id)   # 冲煮统计缓存
            invalidate_ios_cache('recipe_list', request.user.id)          # 配方册缓存（使用次数、最后使用时间）
            invalidate_ios_cache('hindsight_data', request.user.id)       # 后见之明数据缓存
            invalidate_ios_cache('heatmap_data', request.user.id)         # 热力图数据缓存

            # 序列化返回新创建的记录
            from .serializers import IOSBrewingRecordSerializer
            serializer = IOSBrewingRecordSerializer(record)

            logger.info(f"成功创建快速冲煮记录: ID={record.id}，已清除相关缓存")
            return Response({
                'status': 'success',
                'record': serializer.data
            })
        else:
            return Response({
                'status': 'error',
                'message': '无法创建记录'
            }, status=status.HTTP_400_BAD_REQUEST)

    except RecipeThread.DoesNotExist:
        return Response({
            'status': 'error',
            'message': '配方不存在'
        }, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        logger.error(f"快速创建冲煮记录时发生错误: {str(e)}", exc_info=True)
        return Response({
            'status': 'error',
            'message': f'操作失败：{str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['GET'])
@permission_classes([IsAuthenticated])
def preview_quick_brew(request, recipe_id):
    """预览速记功能"""
    try:
        logger.info(f"用户 {request.user.id} 预览配方 {recipe_id} 的速记功能")

        from my.models import RecipeThread, Equipment, CoffeeBean
        recipe = RecipeThread.objects.get(id=recipe_id, user=request.user)
        params = recipe.get_latest_parameters()

        if not params:
            return Response({
                'status': 'error',
                'message': '无法获取配方参数'
            }, status=status.HTTP_400_BAD_REQUEST)

        # 获取最近一条有步骤的记录
        latest_record_with_steps = BrewingRecord.objects.filter(
            user=request.user,
            recipe_name=recipe.recipe_name,
            steps__isnull=False  # 确保steps不为null
        ).exclude(
            steps=[]  # 排除空列表
        ).order_by('-created_at').first()

        # 复制最近一条有步骤的记录的步骤，如果没有则使用空列表
        steps = latest_record_with_steps.steps if latest_record_with_steps else []

        # 获取器具和咖啡豆的详细信息
        try:
            brewing_equipment = Equipment.objects.get(id=params['brewing_equipment'], user=request.user)
            brewing_equipment_name = f"{brewing_equipment.brand} {brewing_equipment.name}" if brewing_equipment.brand else brewing_equipment.name
            brew_method = brewing_equipment.get_brew_method_display() if brewing_equipment.brew_method else "手冲"
        except Equipment.DoesNotExist:
            brewing_equipment_name = "未知器具"
            brew_method = "手冲"

        try:
            grinding_equipment = Equipment.objects.get(id=params['grinding_equipment'], user=request.user)
            grinding_equipment_name = f"{grinding_equipment.brand} {grinding_equipment.name}" if grinding_equipment.brand else grinding_equipment.name
        except Equipment.DoesNotExist:
            grinding_equipment_name = "未知磨豆机"

        try:
            coffee_bean = CoffeeBean.objects.get(id=params['coffee_bean'], user=request.user)
            coffee_bean_name = coffee_bean.name
            coffee_bean_roaster = coffee_bean.roaster
            coffee_bean_roast_level_display = coffee_bean.get_roast_level_display()
            coffee_bean_process = coffee_bean.process or ""
            coffee_bean_origin = coffee_bean.origin or ""
            coffee_bean_region = coffee_bean.region or ""
            coffee_bean_finca = coffee_bean.finca or ""
            coffee_bean_variety = coffee_bean.variety or ""
        except CoffeeBean.DoesNotExist:
            coffee_bean_name = "未知咖啡豆"
            coffee_bean_roaster = ""
            coffee_bean_roast_level_display = ""
            coffee_bean_process = ""
            coffee_bean_origin = ""
            coffee_bean_region = ""
            coffee_bean_finca = ""
            coffee_bean_variety = ""

        # 获取小工具信息
        gadget_names = []
        if params.get('gadget_kit'):
            try:
                gadget_kit = Equipment.objects.get(id=params['gadget_kit'], user=request.user)
                gadget_names.append(f"{gadget_kit.brand} {gadget_kit.name}" if gadget_kit.brand else gadget_kit.name)
            except Equipment.DoesNotExist:
                pass
        elif params.get('gadgets'):
            for gadget_id in params['gadgets']:
                try:
                    gadget = Equipment.objects.get(id=gadget_id, user=request.user)
                    gadget_names.append(f"{gadget.brand} {gadget.name}" if gadget.brand else gadget.name)
                except Equipment.DoesNotExist:
                    pass

        # 构建预览数据
        preview_data = {
            'recipe_name': recipe.recipe_name,
            'brewing_equipment': params['brewing_equipment'],
            'grinding_equipment': params['grinding_equipment'],
            'coffee_bean': params['coffee_bean'],
            'grind_size': params['grind_size'],
            'dose_weight': params['dose_weight'],
            'yield_weight': params['yield_weight'],
            'water_temperature': params['water_temperature'],
            'brewing_time': params['brewing_time'],
            'water_quality': params['water_quality'],
            'steps': steps,
            # 添加显示名称
            'brewing_equipment_name': brewing_equipment_name,
            'grinding_equipment_name': grinding_equipment_name,
            'coffee_bean_name': coffee_bean_name,
            'brew_method': brew_method,
            'gadget_names': gadget_names,
            'user_name': request.user.first_name or request.user.username,
            # 添加咖啡豆详细信息
            'coffee_bean_roaster': coffee_bean_roaster,
            'coffee_bean_roast_level_display': coffee_bean_roast_level_display,
            'coffee_bean_process': coffee_bean_process,
            'coffee_bean_origin': coffee_bean_origin,
            'coffee_bean_region': coffee_bean_region,
            'coffee_bean_finca': coffee_bean_finca,
            'coffee_bean_variety': coffee_bean_variety,
        }

        # 如果有小工具组合
        if params['gadget_kit']:
            preview_data['gadget_kit'] = params['gadget_kit']
        # 如果有单个小工具
        elif params['gadgets']:
            preview_data['gadgets'] = params['gadgets']

        logger.info(f"成功获取配方预览数据: recipe_id={recipe_id}")
        return Response({
            'status': 'success',
            'preview': preview_data
        })

    except RecipeThread.DoesNotExist:
        return Response({
            'status': 'error',
            'message': '配方不存在'
        }, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        logger.error(f"预览速记功能时发生错误: {str(e)}", exc_info=True)
        return Response({
            'status': 'error',
            'message': f'操作失败：{str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
@csrf_exempt
def rename_recipe(request, recipe_id):
    """重命名配方"""
    try:
        logger.info(f"用户 {request.user.id} 重命名配方 {recipe_id}")

        from my.models import RecipeThread, BrewingRecord
        from django.core.cache import cache

        # 获取配方
        recipe = RecipeThread.objects.get(id=recipe_id, user=request.user)
        new_name = request.data.get('new_name', '').strip()

        if not new_name:
            return Response({
                'status': 'error',
                'message': '配方名称不能为空'
            }, status=status.HTTP_400_BAD_REQUEST)

        # 检查新名称是否已存在
        if RecipeThread.objects.filter(user=request.user, recipe_name=new_name).exclude(id=recipe_id).exists():
            return Response({
                'status': 'error',
                'message': '已存在同名配方'
            }, status=status.HTTP_400_BAD_REQUEST)

        # 更新所有相关记录
        old_name = recipe.recipe_name
        BrewingRecord.objects.filter(
            user=request.user,
            recipe_name=old_name
        ).update(recipe_name=new_name)

        # 更新配方主题
        recipe.recipe_name = new_name
        recipe.save()

        # 清除缓存 - 配方重命名后需要刷新多个相关缓存
        cache_key = f'user_recipes_{request.user.id}'
        cache.delete(cache_key)

        # 清除iOS API相关缓存
        invalidate_ios_cache('recipe_list', request.user.id)          # 配方列表缓存
        invalidate_ios_cache('brewlog_list', request.user.id)         # 冲煮记录列表缓存（包含配方名称）
        invalidate_ios_cache('brewlog_statistics', request.user.id)   # 冲煮统计缓存（可能包含配方统计）
        invalidate_ios_cache('hindsight_data', request.user.id)       # 后见之明数据缓存（包含配方分析）
        invalidate_ios_cache('heatmap_data', request.user.id)         # 热力图数据缓存（可能按配方分组）

        # 清除web版本相关缓存
        from my.cache_utils import invalidate_hindsight_cache, invalidate_record_cache
        try:
            invalidate_hindsight_cache(request.user.id)  # 清除后见之明缓存
            invalidate_record_cache(request.user.id)     # 清除冲煮记录缓存
        except Exception as e:
            logger.warning(f"清除web版本缓存时出错: {str(e)}")
            # 不影响主要功能，继续执行

        # 序列化更新后的配方
        from .serializers import IOSRecipeThreadSerializer
        serializer = IOSRecipeThreadSerializer(recipe)

        logger.info(f"成功重命名配方: {old_name} -> {new_name}")
        return Response({
            'status': 'success',
            'message': '重命名成功',
            'recipe': serializer.data
        })

    except RecipeThread.DoesNotExist:
        return Response({
            'status': 'error',
            'message': '配方不存在'
        }, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        logger.error(f"重命名配方时发生错误: {str(e)}", exc_info=True)
        return Response({
            'status': 'error',
            'message': f'操作失败：{str(e)}'
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)