import SwiftUI

// 临时定义，直到OnboardingService被正确导入
struct OnboardingData {
    var beanName: String = ""
    var roasterType: String = ""
    var brewMethod: String = ""
    var grinderType: String = ""
}

struct OnboardingOptions {
    static let roasterTypes = [
        ("home", "自家烘焙"),
        ("commercial", "商业豆商")
    ]

    static let brewMethods = [
        ("POUR_OVER", "手冲"),
        ("FRENCH_PRESS", "法压"),
        ("ESPRESSO", "意式浓缩"),
        ("AEROPRESS", "爱乐压"),
        ("COLD_BREW", "冷萃"),
        ("MOKA_POT", "摩卡壶"),
        ("SIPHON", "虹吸"),
        ("DRIP", "滴滤")
    ]

    static let grinderTypes = [
        ("dedicated", "专用磨豆机"),
        ("built_in", "研磨一体机"),
        ("store", "商家代磨")
    ]
}

// 临时简化的OnboardingService
class TempOnboardingService: ObservableObject {
    static let shared = TempOnboardingService()
    private init() {}

    func submitOnboarding(data: OnboardingData) async throws -> (success: Bool, message: String) {
        // 构建请求数据
        let requestData = [
            "beanName": data.beanName,
            "roasterType": data.roasterType,
            "brewMethod": data.brewMethod,
            "grinderType": data.grinderType
        ]

        // 发送请求到服务器
        guard let url = URL(string: "https://kafeidazi.com/ios/api/auth/onboarding/submit/") else {
            throw URLError(.badURL)
        }

        var request = URLRequest(url: url)
        request.httpMethod = "POST"
        request.addValue("application/json", forHTTPHeaderField: "Content-Type")
        // 这里需要从AuthService获取token，暂时使用空字符串
        request.addValue("Bearer ", forHTTPHeaderField: "Authorization")
        request.httpBody = try JSONSerialization.data(withJSONObject: requestData)

        let (data, response) = try await URLSession.shared.data(for: request)

        guard let httpResponse = response as? HTTPURLResponse else {
            throw URLError(.badServerResponse)
        }

        let responseData = try JSONSerialization.jsonObject(with: data) as? [String: Any]
        let success = responseData?["success"] as? Bool ?? false
        let message = responseData?["message"] as? String ?? "未知错误"

        if httpResponse.statusCode == 200 && success {
            return (success: true, message: message)
        } else {
            return (success: false, message: message)
        }
    }
}

struct OnboardingView: View {
    @Environment(\.dismiss) var dismiss
    @StateObject private var onboardingService = TempOnboardingService.shared

    // 表单状态
    @State private var currentStep = 1
    @State private var formData = OnboardingData()
    @State private var isSubmitting = false
    @State private var errorMessage = ""
    @State private var showError = false

    // 添加完成回调
    let onComplete: (() -> Void)?

    // 添加初始化方法
    init(onComplete: (() -> Void)? = nil) {
        self.onComplete = onComplete
    }

    private let totalSteps = 4

    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // 顶部说明文字
                VStack(alignment: .leading, spacing: 8) {
                    Text("为了更好地为你服务，在开始记录前，请先回答几个简单问题，我们会自动为你准备所需数据。")
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.leading)

                    // 进度条
                    ProgressView(value: Double(currentStep), total: Double(totalSteps))
                        .progressViewStyle(LinearProgressViewStyle(tint: .accentColor))
                        .scaleEffect(y: 2)
                }
                .padding(.horizontal)
                .padding(.top)

                Spacer()

                // 表单内容
                VStack(spacing: 20) {
                    if currentStep == 1 {
                        Step1View(beanName: $formData.beanName)
                    } else if currentStep == 2 {
                        Step2View(roasterType: $formData.roasterType)
                    } else if currentStep == 3 {
                        Step3View(brewMethod: $formData.brewMethod)
                    } else if currentStep == 4 {
                        Step4View(grinderType: $formData.grinderType)
                    }
                }
                .padding(.horizontal)

                Spacer()

                // 底部按钮
                HStack(spacing: 16) {
                    if currentStep > 1 {
                        Button("返回") {
                            withAnimation {
                                currentStep -= 1
                            }
                        }
                        .foregroundColor(.secondary)
                    }

                    Spacer()

                    if currentStep < totalSteps {
                        Button("继续") {
                            if validateCurrentStep() {
                                withAnimation {
                                    currentStep += 1
                                }
                            }
                        }
                        .buttonStyle(.borderedProminent)
                        .disabled(!isCurrentStepValid())
                    } else {
                        Button(action: submitForm) {
                            HStack {
                                if isSubmitting {
                                    ProgressView()
                                        .scaleEffect(0.8)
                                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                } else {
                                    Text("开始记录")
                                }
                            }
                            .frame(minWidth: 100)
                        }
                        .buttonStyle(.borderedProminent)
                        .disabled(isSubmitting || !isCurrentStepValid())
                    }
                }
                .padding()
            }
            .navigationTitle("新手引导")
            .overlay(
                Group {
                    if isSubmitting {
                        ZStack {
                            Color.black.opacity(0.3)
                                .ignoresSafeArea()

                            VStack(spacing: 16) {
                                ProgressView()
                                    .scaleEffect(1.2)
                                    .progressViewStyle(CircularProgressViewStyle(tint: .white))

                                Text("正在创建初始数据...")
                                    .foregroundColor(.white)
                                    .font(.body)
                            }
                            .padding()
                            .background(Color.black.opacity(0.7))
                            .cornerRadius(12)
                        }
                    }
                }
            )
        }
        .alert("错误", isPresented: $showError) {
            Button("确定") {
                showError = false
                errorMessage = ""
            }
        } message: {
            Text(errorMessage)
        }
    }

    // MARK: - 验证方法
    private func validateCurrentStep() -> Bool {
        errorMessage = ""
        showError = false

        switch currentStep {
        case 1:
            if formData.beanName.trimmingCharacters(in: CharacterSet.whitespacesAndNewlines).isEmpty {
                errorMessage = "请输入咖啡豆名称"
                showError = true
                return false
            }
        case 2:
            if formData.roasterType.isEmpty {
                errorMessage = "请选择咖啡豆来源"
                showError = true
                return false
            }
        case 3:
            if formData.brewMethod.isEmpty {
                errorMessage = "请选择冲煮方式"
                showError = true
                return false
            }
        case 4:
            if formData.grinderType.isEmpty {
                errorMessage = "请选择磨豆机类型"
                showError = true
                return false
            }
        default:
            break
        }
        return true
    }

    private func isCurrentStepValid() -> Bool {
        switch currentStep {
        case 1:
            return !formData.beanName.trimmingCharacters(in: CharacterSet.whitespacesAndNewlines).isEmpty
        case 2:
            return !formData.roasterType.isEmpty
        case 3:
            return !formData.brewMethod.isEmpty
        case 4:
            return !formData.grinderType.isEmpty
        default:
            return false
        }
    }

    // MARK: - 提交表单
    private func submitForm() {
        guard validateCurrentStep() else { return }

        isSubmitting = true

        Task {
            do {
                let result = try await onboardingService.submitOnboarding(data: formData)

                await MainActor.run {
                    isSubmitting = false

                    if result.success {
                        // 成功后跳转到新增冲煮记录视图
                        dismiss()
                        onComplete?()

                        // 发送通知跳转到新增记录页面
                        NotificationCenter.default.post(
                            name: NSNotification.Name("NavigateToAddRecord"),
                            object: nil
                        )
                    } else {
                        errorMessage = result.message
                        showError = true
                    }
                }
            } catch {
                await MainActor.run {
                    isSubmitting = false
                    errorMessage = error.localizedDescription
                    showError = true
                }
            }
        }
    }
}

// MARK: - 步骤视图组件

struct OptionButton: View {
    let title: String
    let isSelected: Bool
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            HStack {
                Text(title)
                    .foregroundColor(.primary)
                Spacer()
                Image(systemName: isSelected ? "checkmark.circle.fill" : "circle")
                    .foregroundColor(isSelected ? .accentColor : .secondary)
            }
            .padding()
            .background(isSelected ? Color.accentColor.opacity(0.1) : Color.clear)
            .overlay(
                RoundedRectangle(cornerRadius: 8)
                    .stroke(isSelected ? Color.accentColor : Color.secondary.opacity(0.3), lineWidth: 1)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

struct Step1View: View {
    @Binding var beanName: String

    var body: some View {
        VStack(spacing: 20) {
            VStack(spacing: 8) {
                Text("你正在喝哪款咖啡豆？")
                    .font(.title2)
                    .fontWeight(.semibold)
                    .multilineTextAlignment(.center)
            }

            VStack(alignment: .leading, spacing: 8) {
                TextField("例如：埃塞水洗", text: $beanName)
                    .textFieldStyle(.roundedBorder)
                    .font(.body)
            }
        }
        .padding()
        .background(Color.gray.opacity(0.1))
        .cornerRadius(12)
    }
}

struct Step2View: View {
    @Binding var roasterType: String

    var body: some View {
        VStack(spacing: 20) {
            Text("咖啡豆来源")
                .font(.title2)
                .fontWeight(.semibold)

            VStack(spacing: 12) {
                ForEach(OnboardingOptions.roasterTypes, id: \.0) { option in
                    OptionButton(
                        title: option.1,
                        isSelected: roasterType == option.0,
                        action: { roasterType = option.0 }
                    )
                }
            }
        }
        .padding()
        .background(Color.gray.opacity(0.1))
        .cornerRadius(12)
    }
}

struct Step3View: View {
    @Binding var brewMethod: String

    var body: some View {
        VStack(spacing: 20) {
            Text("冲煮方式")
                .font(.title2)
                .fontWeight(.semibold)

            LazyVGrid(columns: [GridItem(.flexible()), GridItem(.flexible())], spacing: 12) {
                ForEach(OnboardingOptions.brewMethods, id: \.0) { option in
                    GridOptionButton(
                        title: option.1,
                        isSelected: brewMethod == option.0,
                        action: { brewMethod = option.0 }
                    )
                }
            }
        }
        .padding()
        .background(Color.gray.opacity(0.1))
        .cornerRadius(12)
    }
}

struct GridOptionButton: View {
    let title: String
    let isSelected: Bool
    let action: () -> Void

    var body: some View {
        Button(action: action) {
            VStack(spacing: 8) {
                Text(title)
                    .font(.body)
                    .foregroundColor(.primary)

                Image(systemName: isSelected ? "checkmark.circle.fill" : "circle")
                    .foregroundColor(isSelected ? .accentColor : .secondary)
            }
            .frame(maxWidth: .infinity)
            .padding()
            .background(isSelected ? Color.accentColor.opacity(0.1) : Color.clear)
            .overlay(
                RoundedRectangle(cornerRadius: 8)
                    .stroke(isSelected ? Color.accentColor : Color.secondary.opacity(0.3), lineWidth: 1)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
}

struct Step4View: View {
    @Binding var grinderType: String

    var body: some View {
        VStack(spacing: 20) {
            Text("磨豆机类型")
                .font(.title2)
                .fontWeight(.semibold)

            VStack(spacing: 12) {
                ForEach(OnboardingOptions.grinderTypes, id: \.0) { option in
                    OptionButton(
                        title: option.1,
                        isSelected: grinderType == option.0,
                        action: { grinderType = option.0 }
                    )
                }
            }
        }
        .padding()
        .background(Color.gray.opacity(0.1))
        .cornerRadius(12)
    }
}
