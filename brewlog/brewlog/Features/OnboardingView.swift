import SwiftUI

struct OnboardingView: View {
    @Environment(\.dismiss) var dismiss
    @StateObject private var onboardingService = OnboardingService.shared

    // 表单状态
    @State private var currentStep = 1
    @State private var formData = OnboardingData()
    @State private var isSubmitting = false
    @State private var errorMessage = ""
    @State private var showError = false

    // 添加完成回调
    let onComplete: (() -> Void)?

    // 添加初始化方法
    init(onComplete: (() -> Void)? = nil) {
        self.onComplete = onComplete
    }

    private let totalSteps = 4

    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // 顶部说明文字
                VStack(alignment: .leading, spacing: 8) {
                    Text("为了更好地为你服务，在开始记录前，请先回答几个简单问题，我们会自动为你准备所需数据。")
                        .font(.caption)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.leading)

                    // 进度条
                    ProgressView(value: Double(currentStep), total: Double(totalSteps))
                        .progressViewStyle(LinearProgressViewStyle(tint: .accentColor))
                        .scaleEffect(y: 2)
                }
                .padding(.horizontal)
                .padding(.top)

                Spacer()

                // 表单内容
                VStack(spacing: 20) {
                    if currentStep == 1 {
                        Step1View(beanName: $formData.beanName)
                    } else if currentStep == 2 {
                        Step2View(roasterType: $formData.roasterType)
                    } else if currentStep == 3 {
                        Step3View(brewMethod: $formData.brewMethod)
                    } else if currentStep == 4 {
                        Step4View(grinderType: $formData.grinderType)
                    }
                }
                .padding(.horizontal)

                Spacer()

                // 底部按钮
                HStack(spacing: 16) {
                    if currentStep > 1 {
                        Button("返回") {
                            withAnimation {
                                currentStep -= 1
                            }
                        }
                        .foregroundColor(.secondary)
                    }

                    Spacer()

                    if currentStep < totalSteps {
                        Button("继续") {
                            if validateCurrentStep() {
                                withAnimation {
                                    currentStep += 1
                                }
                            }
                        }
                        .buttonStyle(.borderedProminent)
                        .disabled(!isCurrentStepValid())
                    } else {
                        Button(action: submitForm) {
                            HStack {
                                if isSubmitting {
                                    ProgressView()
                                        .scaleEffect(0.8)
                                        .progressViewStyle(CircularProgressViewStyle(tint: .white))
                                } else {
                                    Text("开始记录")
                                }
                            }
                            .frame(minWidth: 100)
                        }
                        .buttonStyle(.borderedProminent)
                        .disabled(isSubmitting || !isCurrentStepValid())
                    }
                }
                .padding()
            }
            .navigationTitle("新手引导")
            .navigationBarTitleDisplayMode(.inline)
            .loading(isSubmitting, text: "正在创建初始数据...", blur: false)
        }
        .alert("错误", isPresented: $showError) {
            Button("确定") {
                showError = false
                errorMessage = ""
            }
        } message: {
            Text(errorMessage)
        }
    }

    // MARK: - 验证方法
    private func validateCurrentStep() -> Bool {
        errorMessage = ""
        showError = false

        switch currentStep {
        case 1:
            if formData.beanName.trimmingCharacters(in: CharacterSet.whitespacesAndNewlines).isEmpty {
                errorMessage = "请输入咖啡豆名称"
                showError = true
                return false
            }
        case 2:
            if formData.roasterType.isEmpty {
                errorMessage = "请选择咖啡豆来源"
                showError = true
                return false
            }
        case 3:
            if formData.brewMethod.isEmpty {
                errorMessage = "请选择冲煮方式"
                showError = true
                return false
            }
        case 4:
            if formData.grinderType.isEmpty {
                errorMessage = "请选择磨豆机类型"
                showError = true
                return false
            }
        default:
            break
        }
        return true
    }

    private func isCurrentStepValid() -> Bool {
        switch currentStep {
        case 1:
            return !formData.beanName.trimmingCharacters(in: CharacterSet.whitespacesAndNewlines).isEmpty
        case 2:
            return !formData.roasterType.isEmpty
        case 3:
            return !formData.brewMethod.isEmpty
        case 4:
            return !formData.grinderType.isEmpty
        default:
            return false
        }
    }

    // MARK: - 提交表单
    private func submitForm() {
        guard validateCurrentStep() else { return }

        isSubmitting = true

        onboardingService.submitOnboarding(data: formData)
            .receive(on: DispatchQueue.main)
            .sink(
                receiveCompletion: { completion in
                    isSubmitting = false
                    if case .failure(let error) = completion {
                        errorMessage = error.localizedDescription
                        showError = true
                    }
                },
                receiveValue: { response in
                    if response.success {
                        // 成功后跳转到新增冲煮记录视图
                        dismiss()
                        onComplete?()

                        // 发送通知跳转到新增记录页面
                        NotificationCenter.default.post(
                            name: NSNotification.Name("NavigateToAddRecord"),
                            object: nil
                        )
                    } else {
                        errorMessage = response.message
                        showError = true
                    }
                }
            )
            .store(in: &onboardingService.cancellables)
    }
}

// MARK: - 步骤视图组件

struct Step1View: View {
    @Binding var beanName: String

    var body: some View {
        VStack(spacing: 20) {
            VStack(spacing: 8) {
                Text("你正在喝哪款咖啡豆？")
                    .font(.title2)
                    .fontWeight(.semibold)
                    .multilineTextAlignment(.center)
            }

            VStack(alignment: .leading, spacing: 8) {
                TextField("例如：埃塞水洗", text: $beanName)
                    .textFieldStyle(.roundedBorder)
                    .font(.body)
            }
        }
        .padding()
        .background(Color.secondaryBg)
        .cornerRadius(12)
    }
}

struct Step2View: View {
    @Binding var roasterType: String

    var body: some View {
        VStack(spacing: 20) {
            VStack(spacing: 8) {
                Text("咖啡豆来源")
                    .font(.title2)
                    .fontWeight(.semibold)
                    .multilineTextAlignment(.center)
            }

            VStack(spacing: 12) {
                ForEach(OnboardingOptions.roasterTypes, id: \.0) { option in
                    Button(action: {
                        roasterType = option.0
                    }) {
                        HStack {
                            Text(option.1)
                                .foregroundColor(.primaryText)
                            Spacer()
                            if roasterType == option.0 {
                                Image(systemName: "checkmark.circle.fill")
                                    .foregroundColor(.accentColor)
                            } else {
                                Image(systemName: "circle")
                                    .foregroundColor(.secondary)
                            }
                        }
                        .padding()
                        .background(roasterType == option.0 ? Color.accentColor.opacity(0.1) : Color.clear)
                        .overlay(
                            RoundedRectangle(cornerRadius: 8)
                                .stroke(roasterType == option.0 ? Color.accentColor : Color.secondary.opacity(0.3), lineWidth: 1)
                        )
                    }
                    .buttonStyle(PlainButtonStyle())
                }
            }
        }
        .padding()
        .background(Color.secondaryBg)
        .cornerRadius(12)
    }
}

struct Step3View: View {
    @Binding var brewMethod: String

    var body: some View {
        VStack(spacing: 20) {
            VStack(spacing: 8) {
                Text("冲煮方式")
                    .font(.title2)
                    .fontWeight(.semibold)
                    .multilineTextAlignment(.center)
            }

            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 12) {
                ForEach(OnboardingOptions.brewMethods, id: \.0) { option in
                    Button(action: {
                        brewMethod = option.0
                    }) {
                        VStack(spacing: 8) {
                            Text(option.1)
                                .font(.body)
                                .foregroundColor(.primaryText)

                            if brewMethod == option.0 {
                                Image(systemName: "checkmark.circle.fill")
                                    .foregroundColor(.accentColor)
                            } else {
                                Image(systemName: "circle")
                                    .foregroundColor(.secondary)
                            }
                        }
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(brewMethod == option.0 ? Color.accentColor.opacity(0.1) : Color.clear)
                        .overlay(
                            RoundedRectangle(cornerRadius: 8)
                                .stroke(brewMethod == option.0 ? Color.accentColor : Color.secondary.opacity(0.3), lineWidth: 1)
                        )
                    }
                    .buttonStyle(PlainButtonStyle())
                }
            }
        }
        .padding()
        .background(Color.secondaryBg)
        .cornerRadius(12)
    }
}

struct Step4View: View {
    @Binding var grinderType: String

    var body: some View {
        VStack(spacing: 20) {
            VStack(spacing: 8) {
                Text("磨豆机类型")
                    .font(.title2)
                    .fontWeight(.semibold)
                    .multilineTextAlignment(.center)
            }

            VStack(spacing: 12) {
                ForEach(OnboardingOptions.grinderTypes, id: \.0) { option in
                    Button(action: {
                        grinderType = option.0
                    }) {
                        HStack {
                            Text(option.1)
                                .foregroundColor(.primaryText)
                            Spacer()
                            if grinderType == option.0 {
                                Image(systemName: "checkmark.circle.fill")
                                    .foregroundColor(.accentColor)
                            } else {
                                Image(systemName: "circle")
                                    .foregroundColor(.secondary)
                            }
                        }
                        .padding()
                        .background(grinderType == option.0 ? Color.accentColor.opacity(0.1) : Color.clear)
                        .overlay(
                            RoundedRectangle(cornerRadius: 8)
                                .stroke(grinderType == option.0 ? Color.accentColor : Color.secondary.opacity(0.3), lineWidth: 1)
                        )
                    }
                    .buttonStyle(PlainButtonStyle())
                }
            }
        }
        .padding()
        .background(Color.secondaryBg)
        .cornerRadius(12)
    }
}
