import SwiftUI

struct ForgotPasswordView: View {
    @Environment(\.dismiss) private var dismiss
    @FocusState private var focusedField: String?
    
    @State private var email: String = ""
    @State private var isLoading = false
    @State private var errorMessage: String?
    @State private var successMessage: String?
    
    var body: some View {
        ZStack(alignment: .top) {
            // 固定的顶部导航栏
            topNavigationBar
                .background(Color.primaryBg)
                .zIndex(1)
            
            ScrollView {
                VStack(spacing: 8) {
                    // 添加一个占位符，高度等于导航栏高度
                    Color.clear
                        .frame(height: 52)
                    
                    // 主要内容区域
                    VStack(spacing: 16) {
                        // 说明信息区域
                        explanationSection
                        
                        // 邮箱输入区域
                        emailInputSection
                        
                        // 成功信息区域
                        if let successMessage = successMessage {
                            successSection(successMessage)
                        }
                        
                        // 错误信息区域
                        if let errorMessage = errorMessage {
                            errorSection(errorMessage)
                        }
                        
                        // 发送按钮区域
                        actionButtonsSection
                    }
                    .padding(.horizontal, 12)
                    .padding(.bottom, 20)
                }
            }
            .background(
                Color.primaryBg
                    .onTapGesture {
                        hideKeyboard()
                    }
            )
            .edgesIgnoringSafeArea(.bottom)
        }
        .overlay(
            Group {
                if isLoading {
                    ZStack {
                        Color.black.opacity(0.4)
                        
                        BlinkingLoader(
                            color: .primaryBg,
                            width: 12,
                            height: 18,
                            duration: 1.5,
                            text: "处理中..."
                        )
                        .padding()
                        .background(Color.secondaryBg.opacity(0.9))
                        .cornerRadius(10)
                        .shadow(radius: 5)
                    }
                }
            }
        )
    }
    
    // 顶部导航栏
    private var topNavigationBar: some View {
        ZStack {
            HStack {
                Button(action: {
                    dismiss()
                }) {
                    Text("取消")
                        .foregroundColor(.linkText)
                }
                
                Spacer()
            }
        }
        .padding(.vertical, 12)
        .padding(.horizontal, 16)
        .background(Color.primaryBg)
    }
    
    // 说明信息区域
    private var explanationSection: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Image("forgetPassword.symbols")
                    .foregroundColor(.functionText)
                    .font(.title2)
                
                Text("找回密码")
                    .font(.headline)
                    .foregroundColor(.primaryText)
            }
            
            Text("请输入您的注册邮箱，我们将向您发送密码重置链接。")
                .font(.body)
                .foregroundColor(.noteText)
                .fixedSize(horizontal: false, vertical: true)
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 16)
        .background(Color.primaryBg)
        .cornerRadius(10)
    }
    
    // 邮箱输入区域
    private var emailInputSection: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("邮箱地址")
                .font(.system(size: 16, weight: .regular))
                .foregroundColor(.primaryText)
            
            TextField("请输入您注册时使用的邮箱地址", text: $email)
                .font(.system(size: 17))
                .padding(.vertical, 14)
                .padding(.horizontal, 16)
                .background(Color.secondaryBg)
                .cornerRadius(12)
                .disableAutocorrection(true)
                .autocapitalization(.none)
                .keyboardType(.emailAddress)
                .submitLabel(.done)
                .focused($focusedField, equals: "email_field")
                .onChange(of: email) { _ in
                    clearMessages()
                }
            
            Text("请确保输入正确的邮箱地址，以便接收重置密码的邮件")
                .font(.caption)
                .foregroundColor(.noteText)
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 16)
        .background(Color.primaryBg)
        .cornerRadius(10)
    }
    
    // 成功信息区域
    private func successSection(_ message: String) -> some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Image(systemName: "checkmark.circle.fill")
                    .foregroundColor(.green)
                
                Text("发送成功")
                    .foregroundColor(.green)
                    .font(.headline)
            }
            
            Text(message)
                .foregroundColor(.primaryText)
                .fixedSize(horizontal: false, vertical: true)
            
            Text("请检查您的邮箱（包括垃圾邮件文件夹），点击邮件中的链接重置密码。")
                .font(.caption)
                .foregroundColor(.noteText)
                .fixedSize(horizontal: false, vertical: true)
            
            Button(action: {
                dismiss()
            }) {
                HStack {
                    Image("loginAccount.symbols")
                    Text("返回登录")
                }
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(.linkText)
                .frame(maxWidth: .infinity)
                .padding(.vertical, 12)
                .background(Color.secondaryBg)
                .cornerRadius(12)
            }
            .buttonStyle(PlainButtonStyle())
            .padding(.top, 8)
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 16)
        .background(Color.primaryBg)
        .cornerRadius(10)
    }
    
    // 错误信息区域
    private func errorSection(_ message: String) -> some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Image(systemName: "exclamationmark.triangle.fill")
                    .foregroundColor(.error)
                
                Text("发送失败")
                    .foregroundColor(.error)
                    .font(.headline)
            }
            
            Text(message)
                .foregroundColor(.error)
                .fixedSize(horizontal: false, vertical: true)
            
            Button(action: sendResetEmail) {
                HStack {
                    Image(systemName: "arrow.clockwise")
                    Text("重试")
                }
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(.linkText)
                .frame(maxWidth: .infinity)
                .padding(.vertical, 12)
                .background(Color.secondaryBg)
                .cornerRadius(12)
            }
            .buttonStyle(PlainButtonStyle())
            .padding(.top, 4)
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 16)
        .background(Color.primaryBg)
        .cornerRadius(10)
    }
    
    // 发送按钮区域
    private var actionButtonsSection: some View {
        VStack(spacing: 16) {
            if successMessage == nil {
                Button(action: sendResetEmail) {
                    HStack {
                        if isLoading {
                            ProgressView()
                                .scaleEffect(0.8)
                                .frame(width: 16, height: 16)
                                .progressViewStyle(CircularProgressViewStyle(tint: .white))
                        } else {
                            Image(systemName: "paperplane.fill")
                        }
                        Text("发送重置邮件")
                    }
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 14)
                    .background(isValidEmail && !isLoading ? Color.functionText : Color.gray.opacity(0.5))
                    .cornerRadius(12)
                }
                .buttonStyle(PlainButtonStyle())
                .disabled(isLoading || !isValidEmail || successMessage != nil)
            }
            
            // 添加Powered by链接
            HStack(spacing: 0) {
                Text("Powered by 咖啡搭子")
                    .font(.caption2)
                    .foregroundColor(.noteText)
                
                Link("(kafeidazi.com)", destination: URL(string: "https://www.kafeidazi.com")!)
                    .font(.caption2)
                    .foregroundColor(.noteText)
            }
            .frame(maxWidth: .infinity, alignment: .center)
            .padding(.top, 8)
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 16)
        .background(Color.primaryBg)
        .cornerRadius(10)
    }
    
    // MARK: - 计算属性
    
    private var isValidEmail: Bool {
        !email.isEmpty && isValidEmailFormat(email)
    }
    
    // MARK: - 方法
    
    // 隐藏键盘方法
    private func hideKeyboard() {
        UIApplication.shared.sendAction(#selector(UIResponder.resignFirstResponder), to: nil, from: nil, for: nil)
    }
    
    private func sendResetEmail() {
        guard isValidEmail else {
            errorMessage = "请输入有效的邮箱地址"
            return
        }

        isLoading = true
        clearMessages()
        hideKeyboard()

        Task {
            do {
                // 调用APIService的密码重置方法
                let response = try await APIService.shared.resetPassword(email: email)

                await MainActor.run {
                    isLoading = false
                    successMessage = response.message
                }
            } catch {
                await MainActor.run {
                    isLoading = false
                    errorMessage = "发送失败: \(error.localizedDescription)"
                }
            }
        }
    }
    
    private func clearMessages() {
        errorMessage = nil
        successMessage = nil
    }
    
    private func isValidEmailFormat(_ email: String) -> Bool {
        let emailRegex = "[A-Z0-9a-z._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,64}"
        let emailPredicate = NSPredicate(format:"SELF MATCHES %@", emailRegex)
        return emailPredicate.evaluate(with: email)
    }
}

#Preview {
    ForgotPasswordView()
}
