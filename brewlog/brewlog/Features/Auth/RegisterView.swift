import SwiftUI
#if canImport(UIKit)
import UIKit
#endif

struct RegisterView: View {
    @EnvironmentObject var authService: AuthService
    @Environment(\.dismiss) private var dismiss
    @FocusState private var focusedField: String?

    @State private var username: String = ""
    @State private var email: String = ""
    @State private var password: String = ""
    @State private var confirmPassword: String = ""
    @State private var isLoading = false
    @State private var errorMessage: String?
    @State private var successMessage: String?
    @State private var showPasswordVisibility = false
    @State private var showOnboarding = false
    @State private var needsEmailVerification = false
    @State private var usernameCheckMessage: String?
    @State private var isCheckingUsername = false
    @State private var showUserAgreement = false
    @State private var showPrivacyPolicy = false
    @State private var usernameCheckTask: Task<Void, Never>?
    @State private var showPasswordMismatch = false
    @State private var isRegistrationCompleted = false // 新增：标记注册是否完成
    @State private var keyboardHeight: CGFloat = 0

    // 频率限制相关状态
    @State private var isUsernameGenerationRateLimited = false
    @State private var rateLimitTimer: Timer?

    // 用于标识当前消息的唯一ID，避免延迟执行冲突
    @State private var currentMessageId: UUID?

    // 标识是否正在程序化设置用户名（避免触发onChange检查）
    @State private var isProgrammaticallySettingUsername = false

    var onRegisterSuccess: (() -> Void)?

    // 本地不可用用户名列表（优先级高于服务端检查）
    private static let localUnavailableUsernames = ["admin", "administrator", "system", "kafeidazi"]
    
    var body: some View {
        ZStack(alignment: .top) {
            // 固定的顶部导航栏
            topNavigationBar
                .background(Color.primaryBg)
                .zIndex(1)
            
            ScrollView {
                VStack(spacing: 8) {
                    // 添加一个占位符，高度等于导航栏高度
                    Color.clear
                        .frame(height: 52)
                    
                    // 主要内容区域
                    VStack(spacing: 16) {
                        if successMessage != nil {
                            // 注册成功后只显示成功信息和开始使用按钮
                            VStack(spacing: 24) {
                                Spacer()

                                // 成功信息区域（居中显示）
                                if let successMessage = successMessage {
                                    successSectionCentered(successMessage)
                                }

                                // 开始使用按钮（居中显示）
                                startUsingButton

                                Spacer()
                            }
                        } else {
                            // 注册前显示完整表单
                            // 基本信息区域
                            basicInfoSection

                            // 密码设置区域
                            passwordSection

                            // 错误信息区域
                            if let errorMessage = errorMessage {
                                errorSection(errorMessage)
                            }

                            // 操作按钮区域
                            actionButtonsSection

                            // 添加底部空间以确保键盘不遮挡内容
                            Spacer().frame(height: keyboardHeight)
                        }
                    }
                    .padding(.horizontal, 12)
                    .padding(.bottom, 20)
                }
            }
            .background(
                Color.primaryBg
                    .onTapGesture {
                        hideKeyboard()
                    }
            )
            .edgesIgnoringSafeArea(.bottom)
            // 添加键盘响应
            .onAppear {
                NotificationCenter.default.addObserver(forName: UIResponder.keyboardWillShowNotification, object: nil, queue: .main) { notification in
                    guard let keyboardFrame = notification.userInfo?[UIResponder.keyboardFrameEndUserInfoKey] as? CGRect else { return }
                    // 添加额外空间以确保视图上移足够距离
                    keyboardHeight = keyboardFrame.height + 20
                }
                
                NotificationCenter.default.addObserver(forName: UIResponder.keyboardWillHideNotification, object: nil, queue: .main) { _ in
                    keyboardHeight = 0
                }
            }
            .onDisappear {
                NotificationCenter.default.removeObserver(self, name: UIResponder.keyboardWillShowNotification, object: nil)
                NotificationCenter.default.removeObserver(self, name: UIResponder.keyboardWillHideNotification, object: nil)
                // 视图销毁时清理定时器
                cleanupTimers()
            }
            .animation(.easeOut(duration: 0.25), value: keyboardHeight)
        }
        .overlay(
            Group {
                if isLoading {
                    ZStack {
                        Color.black.opacity(0.4)
                            .edgesIgnoringSafeArea(.all)
                        
                        BlinkingLoader(
                            color: .primaryBg,
                            width: 12,
                            height: 18,
                            duration: 1.5,
                            text: "注册中..."
                        )
                        .padding()
                        .background(Color.black.opacity(0.5))
                    }
                }
            }
        )
        .sheet(isPresented: $showOnboarding) {
            OnboardingView(onComplete: {
                // OnboardingView 完成后，设置认证状态并获取用户信息
                Task {
                    await authService.completeRegistrationFromToken()
                    await MainActor.run {
                        // 调用成功回调
                        onRegisterSuccess?()
                    }
                }
            })
                .interactiveDismissDisabled(true)
        }
    }
    
    // 顶部导航栏
    private var topNavigationBar: some View {
        ZStack {
            HStack {
                Button(action: {
                    dismiss()
                }) {
                    Text("取消")
                        .foregroundColor(.linkText)
                }
                
                Spacer()
            }
        }
        .padding(.vertical, 12)
        .padding(.horizontal, 16)
        .background(Color.primaryBg)
    }
    
    // 基本信息区域
    private var basicInfoSection: some View {
        VStack(spacing: 16) {
            VStack(alignment: .leading, spacing: 8) {
                // 用户名输入
                VStack(alignment: .leading, spacing: 8) {
                    HStack {
                        Text("注册用户名")
                            .font(.system(size: 16, weight: .regular))
                            .foregroundColor(.primaryText)

                        Spacer()

                        // 只在未触发频率限制时显示「帮我取名」按钮
                        if !isUsernameGenerationRateLimited {
                            Button(action: {
                                generateAndFillUsername()
                            }) {
                                Text("帮我取名")
                                    .font(.system(size: 14))
                                    .foregroundColor(.linkText)
                            }
                            .buttonStyle(PlainButtonStyle())
                        }
                    }
                    
                    TextField("请输入用户名 (至少5个字符)", text: $username)
                        .font(.system(size: 17))
                        .padding(.vertical, 14)
                        .padding(.horizontal, 16)
                        .background(Color.secondaryBg)
                        .cornerRadius(12)
                        .disableAutocorrection(true)
                        .autocapitalization(.none)
                        .submitLabel(.next)
                        .focused($focusedField, equals: "username_field")
                        .onChange(of: username) { _ in
                            clearError()
                            // 只有在非程序化设置时才检查用户名可用性
                            if !isProgrammaticallySettingUsername {
                                checkUsernameAvailability()
                            }
                        }
                        .textContentType(.username)
                    
                    // 用户名检测结果
                    if let usernameCheckMessage = usernameCheckMessage {
                        HStack {
                            Image(systemName: usernameCheckMessage.contains("可用") ? "checkmark.circle.fill" : "xmark.circle.fill")
                                .foregroundColor(usernameCheckMessage.contains("可用") ? .green : .red)

                            Text(usernameCheckMessage)
                                .font(.caption)
                                .foregroundColor(usernameCheckMessage.contains("可用") ? .green : .red)
                        }
                    }
                    
                    if isCheckingUsername {
                        HStack {
                            ProgressView()
                                .scaleEffect(0.8)
                                .frame(width: 16, height: 16)
                            Text("检查用户名...")
                                .font(.caption)
                                .foregroundColor(.noteText)
                        }
                    }
                }
                
                // 邮箱输入
                VStack(alignment: .leading, spacing: 8) {
                    Text("注册邮箱")
                        .font(.system(size: 16, weight: .regular))
                        .foregroundColor(.primaryText)
                    
                    TextField("请输入电子邮箱地址", text: $email)
                        .font(.system(size: 17))
                        .padding(.vertical, 14)
                        .padding(.horizontal, 16)
                        .background(Color.secondaryBg)
                        .cornerRadius(12)
                        .disableAutocorrection(true)
                        .autocapitalization(.none)
                        .keyboardType(.emailAddress)
                        .submitLabel(.next)
                        .focused($focusedField, equals: "email_field")
                        .onChange(of: email) { _ in
                            clearError()
                        }
                        .textContentType(.emailAddress)
                }
                
                Text("请输入有效邮箱，它是您唯一的账号找回方式")
                    .font(.caption)
                    .foregroundColor(.noteText)
                    .fixedSize(horizontal: false, vertical: true)
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 16)
            .background(Color.primaryBg)
            .cornerRadius(10)
        }
    }
    
    // 密码设置区域
    private var passwordSection: some View {
        VStack(spacing: 16) {
            VStack(alignment: .leading, spacing: 8) {
                Text("密码设置")
                    .font(.system(size: 16, weight: .medium))
                    .foregroundColor(.primaryText)
            }
            .frame(maxWidth: .infinity, alignment: .leading)
            
            // 密码输入
            HStack {
                if showPasswordVisibility {
                    TextField("密码", text: $password)
                        .font(.system(size: 17))
                        .disableAutocorrection(true)
                        .submitLabel(.next)
                        .focused($focusedField, equals: "password_field")
                        .onChange(of: password) { _ in
                            clearError()
                            // 如果已经输入了确认密码，检查是否匹配
                            if !confirmPassword.isEmpty {
                                checkPasswordMatch()
                            }
                        }
                        .textContentType(.newPassword)
                } else {
                    SecureField("密码", text: $password)
                        .font(.system(size: 17))
                        .disableAutocorrection(true)
                        .submitLabel(.next)
                        .focused($focusedField, equals: "password_field")
                        .onChange(of: password) { _ in
                            clearError()
                            // 如果已经输入了确认密码，检查是否匹配
                            if !confirmPassword.isEmpty {
                                checkPasswordMatch()
                            }
                        }
                        .textContentType(.newPassword)
                }

                Button(action: {
                    showPasswordVisibility.toggle()
                }) {
                    Image(systemName: showPasswordVisibility ? "eye.slash" : "eye")
                        .foregroundColor(.functionText)
                        .frame(width: 24, height: 24)
                        .contentShape(Rectangle())
                }
                .buttonStyle(PlainButtonStyle())
            }
            .padding(.vertical, 14)
            .padding(.horizontal, 16)
            .background(Color.secondaryBg)
            .cornerRadius(12)
            
            // 确认密码输入
            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    if showPasswordVisibility {
                        TextField("确认密码", text: $confirmPassword)
                            .font(.system(size: 17))
                            .disableAutocorrection(true)
                            .submitLabel(.done)
                            .focused($focusedField, equals: "confirmPassword_field")
                            .onChange(of: confirmPassword) { _ in
                                clearError()
                                checkPasswordMatch()
                            }
                            .textContentType(.newPassword)
                    } else {
                        SecureField("确认密码", text: $confirmPassword)
                            .font(.system(size: 17))
                            .disableAutocorrection(true)
                            .submitLabel(.done)
                            .focused($focusedField, equals: "confirmPassword_field")
                            .onChange(of: confirmPassword) { _ in
                                clearError()
                                checkPasswordMatch()
                            }
                            .textContentType(nil)
                            .id("confirm_password_field")
                            .autocapitalization(.none)
                    }

                    Button(action: {
                        showPasswordVisibility.toggle()
                    }) {
                        Image(systemName: showPasswordVisibility ? "eye.slash" : "eye")
                            .foregroundColor(.functionText)
                            .frame(width: 24, height: 24)
                            .contentShape(Rectangle())
                    }
                    .buttonStyle(PlainButtonStyle())
                }
                .padding(.vertical, 14)
                .padding(.horizontal, 16)
                .background(Color.secondaryBg)
                .cornerRadius(12)
                
                // 密码不匹配提示
                if showPasswordMismatch {
                    HStack {
                        Image(systemName: "exclamationmark.triangle.fill")
                            .foregroundColor(.red)
                            .font(.system(size: 12))
                        
                        Text("两次输入的密码不一致")
                            .font(.caption)
                            .foregroundColor(.red)
                    }
                    .padding(.leading, 4)
                }
            }

            Text("密码至少8个字符，需包含字母和数字")
                .font(.caption)
                .foregroundColor(.noteText)
                .fixedSize(horizontal: false, vertical: true)
                .frame(maxWidth: .infinity, alignment: .leading)
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 16)
        .background(Color.primaryBg)
        .cornerRadius(10)
    }

    // 居中的成功信息区域（用于注册成功后的独占显示）
    private func successSectionCentered(_ message: String) -> some View {
        VStack(spacing: 20) {
            HStack {
                // 成功图标
                Image(systemName: "checkmark.circle.fill")
                    .foregroundColor(.green)

                // 成功标题
                Text("注册成功")
                    .font(.headline)
                    .foregroundColor(.green)
            }
            // 成功消息
            Text(message)
                .foregroundColor(.primaryText)
                .fixedSize(horizontal: false, vertical: true)

            // 邮箱验证提示
            if needsEmailVerification {
                Text("请及时检查邮箱并点击验证链接，以确保账号安全性。")
                    .font(.caption)
                    .foregroundColor(.noteText)
                    .fixedSize(horizontal: false, vertical: true)
            }
        }
        .padding(.vertical, 32)
    }

    // 开始使用按钮（用于注册成功后的独占显示）
    private var startUsingButton: some View {
        Button(action: {
            // 注册成功后，总是显示 OnboardingView
            showOnboarding = true
        }) {
            HStack {
                Image(systemName: "arrow.right.circle.fill")
                    .foregroundColor(.linkText)
                Text("开始使用")
                    .foregroundColor(.linkText)
            }
            .font(.system(size: 16, weight: .medium))
            .frame(maxWidth: .infinity)
            .padding(.vertical, 14)
            .background(Color.secondaryBg)
            .cornerRadius(12)
        }
        .buttonStyle(PlainButtonStyle())
        .padding(.horizontal, 16)
    }
    
    // 错误信息区域
    private func errorSection(_ message: String) -> some View {
        HStack(alignment: .top, spacing: 8) {
            Image(systemName: "exclamationmark.triangle.fill")
                .foregroundColor(.red)
                .font(.system(size: 16))

            Text("注册失败：\(message)")
                .font(.system(size: 15))
                .foregroundColor(.red)
                .fixedSize(horizontal: false, vertical: true)
                .multilineTextAlignment(.leading)
        }
        .padding(.vertical, 8)
        .padding(.horizontal, 16)
        .background(Color.primaryBg)
        .cornerRadius(10)
    }
    
    // 操作按钮区域
    private var actionButtonsSection: some View {
        VStack(spacing: 16) {
            // 注册按钮
            Button(action: register) {
                HStack {
                    if isLoading {
                        ProgressView()
                            .scaleEffect(0.8)
                            .frame(width: 16, height: 16)
                            .progressViewStyle(CircularProgressViewStyle(tint: .primaryBg))
                    } else {
                        Image("newAccount.symbols")
                    }
                    Text("注册")
                        .font(.system(size: 16, weight: .medium))
                }
                .foregroundColor(.primaryBg)
                .frame(maxWidth: .infinity)
                .padding(.vertical, 14)
                .background(isFormValid && !isLoading ? Color.functionText : Color.gray.opacity(0.5))
                .cornerRadius(12)
            }
            .buttonStyle(PlainButtonStyle())
            .disabled(isLoading || !isFormValid || successMessage != nil)
            .padding(.horizontal, 16)
            
            // 用户协议和隐私政策提示
            HStack(spacing: 0) {
                Text("注册即代表您已阅读并同意")
                    .font(.caption)
                    .foregroundColor(.noteText)
                
                Button(action: {
                    showUserAgreement = true
                }) {
                    Text(" 《用户协议》")
                        .font(.caption)
                        .foregroundColor(.functionText)
                }
                
                Button(action: {
                    showPrivacyPolicy = true
                }) {
                    Text(" 《隐私政策》")
                        .font(.caption)
                        .foregroundColor(.functionText)
                }
            }
            .padding(.horizontal, 16)
            
            // 添加Powered by链接
            HStack(spacing: 0) {
                Text("Powered by 咖啡搭子")
                    .font(.caption2)
                    .foregroundColor(.noteText)
                
                Link("(kafeidazi.com)", destination: URL(string: "https://www.kafeidazi.com")!)
                    .font(.caption2)
                    .foregroundColor(.noteText)
            }
            .frame(maxWidth: .infinity, alignment: .center)
            .padding(.top, 4)
            .padding(.horizontal, 16)
        }
        .padding(.vertical, 16)
        .background(Color.primaryBg)
        .cornerRadius(10)
        .sheet(isPresented: $showUserAgreement) {
            UserAgreementView()
                .presentationDragIndicator(.visible)
        }
        .sheet(isPresented: $showPrivacyPolicy) {
            PrivacyPolicyView()
                .presentationDragIndicator(.visible)
        }
    }
    
    // MARK: - 计算属性
    
    private var isFormValid: Bool {
        !username.isEmpty &&
        username.count >= 5 &&
        isValidUsername(username) &&
        !email.isEmpty &&
        !password.isEmpty &&
        !confirmPassword.isEmpty &&
        password == confirmPassword &&
        isValidEmail(email) &&
        password.count >= 8
    }
    
    // MARK: - 方法

    // 隐藏键盘方法
    private func hideKeyboard() {
        #if canImport(UIKit)
        UIApplication.shared.sendAction(#selector(UIResponder.resignFirstResponder), to: nil, from: nil, for: nil)
        keyboardHeight = 0
        #endif
    }

    // 处理用户名生成频率限制
    private func handleUsernameGenerationRateLimit(retryAfter: TimeInterval = 60) {
        isUsernameGenerationRateLimited = true

        // 清除之前的定时器
        rateLimitTimer?.invalidate()

        // 设置新的定时器
        rateLimitTimer = Timer.scheduledTimer(withTimeInterval: retryAfter, repeats: false) { _ in
            DispatchQueue.main.async {
                self.isUsernameGenerationRateLimited = false
                self.rateLimitTimer = nil
                print("✅ 用户名生成频率限制已解除")
            }
        }

        print("⏱️ 用户名生成频率限制已启动，将在 \(Int(retryAfter)) 秒后解除")
    }

    // 设置用户名检测结果并启动3秒自动隐藏
    private func setUsernameCheckMessage(_ message: String?) {
        // 如果新消息和当前消息完全相同，直接返回，避免任何UI更新
        if usernameCheckMessage == message {
            return
        }

        // 如果新消息和当前消息相同，且已经有定时器在运行，则不重复设置
        if let currentMessage = usernameCheckMessage,
           let newMessage = message,
           currentMessage == newMessage,
           currentMessageId != nil {
            return
        }

        // 清除之前的消息ID（这会让之前的定时器失效）
        currentMessageId = nil

        // 设置消息
        usernameCheckMessage = message

        // 如果消息不为空，启动3秒后自动隐藏
        if let currentMessage = message {
            let messageId = UUID()
            currentMessageId = messageId

            // 使用DispatchQueue.main.asyncAfter替代Timer
            DispatchQueue.main.asyncAfter(deadline: .now() + 3.0) {
                // 只有当前消息ID匹配且消息内容也匹配时才清除
                if self.currentMessageId == messageId && self.usernameCheckMessage == currentMessage {
                    self.usernameCheckMessage = nil
                    self.currentMessageId = nil
                }
            }
        }
    }

    // 清理定时器（在视图销毁时调用）
    private func cleanupTimers() {
        rateLimitTimer?.invalidate()
        rateLimitTimer = nil
        // 清除消息ID以防止延迟执行的代码块生效
        currentMessageId = nil
        // 立即清除消息
        usernameCheckMessage = nil
    }
    
    private func register() {
        guard isFormValid else {
            errorMessage = "请完善必填信息"
            return
        }

        isLoading = true
        errorMessage = nil
        successMessage = nil
        hideKeyboard()

        // 使用真实的API调用进行注册
        Task {
            do {
                // 直接调用API注册，不通过AuthService（避免立即设置认证状态）
                let response = try await APIService.shared.register(
                    username: username,
                    email: email,
                    password: password
                )

                await MainActor.run {
                    isLoading = false
                    successMessage = "注册成功！欢迎使用咖啡札记"
                    needsEmailVerification = true
                    isRegistrationCompleted = true

                    // 保存注册返回的token，但不设置认证状态
                    UserDefaults.standard.set(response.access, forKey: "auth_token")

                    print("✅ 注册成功，token已保存，等待用户完成引导")
                }

            } catch let error as APIError {
                await MainActor.run {
                    isLoading = false
                    errorMessage = error.userFriendlyMessage
                    print("❌ 注册失败 (APIError): \(error.userFriendlyMessage)")
                }
            } catch {
                await MainActor.run {
                    isLoading = false
                    errorMessage = error.localizedDescription
                    print("❌ 注册失败 (其他错误): \(error.localizedDescription)")
                }
            }
        }
    }
    
    private func clearError() {
        errorMessage = nil
        successMessage = nil
    }

    // 检查密码是否匹配
    private func checkPasswordMatch() {
        if !confirmPassword.isEmpty {
            // 延迟500毫秒显示不匹配提示，避免用户输入过程中就提示
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                showPasswordMismatch = password != confirmPassword && !confirmPassword.isEmpty
            }
        } else {
            showPasswordMismatch = false
        }
    }

    private func checkUsernameAvailability() {
        // 如果当前正在检查中，跳过重复检查
        if isCheckingUsername {
            return
        }

        guard !username.isEmpty && username.count >= 5 && isValidUsername(username) else {
            // 当用户名不符合条件时清除消息
            setUsernameCheckMessage(nil)
            // 取消之前的任务
            usernameCheckTask?.cancel()
            return
        }

        // 取消之前的任务
        usernameCheckTask?.cancel()

        // 创建新任务，延迟3秒检查
        usernameCheckTask = Task {
            // 等待3秒，让用户完成输入
            try? await Task.sleep(nanoseconds: 3_000_000_000)

            // 检查任务是否被取消
            if Task.isCancelled {
                return
            }

            await MainActor.run {
                isCheckingUsername = true
                // 不要清除消息，让之前的消息继续显示直到有新结果
            }

            // 首先检查本地不可用用户名列表（优先级最高）
            if Self.localUnavailableUsernames.contains(username.lowercased()) {
                await MainActor.run {
                    isCheckingUsername = false
                    setUsernameCheckMessage("用户名已被占用")
                }
                return
            }

            // 使用真实的API调用检测用户名可用性
            do {
                let response = try await APIService.shared.checkUsernameAvailability(username: username)

                await MainActor.run {
                    isCheckingUsername = false
                    if response.available {
                        setUsernameCheckMessage("用户名可用")
                    } else {
                        setUsernameCheckMessage(response.message)
                    }
                }
            } catch APIError.rateLimited(let message) {
                await MainActor.run {
                    isCheckingUsername = false
                    setUsernameCheckMessage("请求过于频繁，请稍后再试")
                    // 触发频率限制时隐藏「帮我取名」按钮
                    handleUsernameGenerationRateLimit()
                    print("⏱️ 用户名检查遇到速率限制: \(message)")
                }
            } catch {
                await MainActor.run {
                    isCheckingUsername = false

                    // 检查是否是速率限制错误（通过错误描述判断）
                    let errorDescription = String(describing: error)
                    if errorDescription.contains("rateLimited") || errorDescription.contains("429") || errorDescription.contains("请求过于频繁") {
                        setUsernameCheckMessage("请求过于频繁，请稍后再试")
                        // 触发频率限制时隐藏「帮我取名」按钮
                        handleUsernameGenerationRateLimit()
                        print("⏱️ 用户名检查遇到速率限制")
                    } else {
                        // 如果是其他API调用失败，回退到本地验证
                        // 使用与服务端一致的黑名单
                        let serverBlacklist = ["admin"]

                        if serverBlacklist.contains(username.lowercased()) {
                            setUsernameCheckMessage("该用户名不可用")
                        } else {
                            // 本地黑名单已经在前面检查过了，这里直接返回可用
                            setUsernameCheckMessage("用户名可用")
                        }
                        print("❌ 用户名检查API失败，回退到本地验证: \(error)")
                    }
                }
            }
        }
    }

    // 使用改进的Snowflake算法生成并直接填写用户名
    private func generateAndFillUsername() {
        let generatedUsername = SnowflakeUsernameGenerator.shared.generateValidUsername()

        // 设置标志，避免触发onChange检查
        isProgrammaticallySettingUsername = true
        username = generatedUsername
        isProgrammaticallySettingUsername = false

        print("🎲 使用SnowflakeUsernameGenerator生成新用户名: \(generatedUsername)")

        // 生成后立即检查可用性（这里不用延迟，因为是自动生成）
        // 取消之前的任务
        usernameCheckTask?.cancel()
        
        isCheckingUsername = true
        // 不要清除消息，让之前的消息继续显示直到有新结果

        // 首先检查本地不可用用户名列表（优先级最高）
        if Self.localUnavailableUsernames.contains(username.lowercased()) {
            isCheckingUsername = false
            setUsernameCheckMessage("用户名已被占用")
            return
        }

        // 使用真实的API调用检测用户名可用性
        Task {
            do {
                let response = try await APIService.shared.checkUsernameAvailability(username: username)

                await MainActor.run {
                    isCheckingUsername = false
                    if response.available {
                        setUsernameCheckMessage("用户名可用")
                    } else {
                        setUsernameCheckMessage(response.message)
                    }
                }
            } catch APIError.rateLimited(let message) {
                await MainActor.run {
                    isCheckingUsername = false
                    setUsernameCheckMessage("请求过于频繁，请稍后再试")
                    // 触发频率限制时隐藏「帮我取名」按钮
                    handleUsernameGenerationRateLimit()
                    print("⏱️ 用户名检查遇到速率限制: \(message)")
                }
            } catch {
                await MainActor.run {
                    isCheckingUsername = false

                    // 检查是否是速率限制错误（通过错误描述判断）
                    let errorDescription = String(describing: error)
                    if errorDescription.contains("rateLimited") || errorDescription.contains("429") || errorDescription.contains("请求过于频繁") {
                        setUsernameCheckMessage("请求过于频繁，请稍后再试")
                        // 触发频率限制时隐藏「帮我取名」按钮
                        handleUsernameGenerationRateLimit()
                        print("⏱️ 用户名检查遇到速率限制")
                    } else {
                        // 如果是其他API调用失败，回退到本地验证
                        // 使用与服务端一致的黑名单
                        let serverBlacklist = ["admin"]

                        if serverBlacklist.contains(username.lowercased()) {
                            setUsernameCheckMessage("该用户名不可用")
                        } else {
                            // 本地黑名单已经在前面检查过了，这里直接返回可用
                            setUsernameCheckMessage("用户名可用")
                        }
                        print("❌ 用户名检查API失败，回退到本地验证: \(error)")
                    }
                }
            }
        }
    }

    // 异步生成唯一用户名（包含服务端验证）
    private func generateUniqueUsernameAsync() {
        Task {
            do {
                let generatedUsername = try await SnowflakeUsernameGenerator.shared.generateUniqueUsername()
                await MainActor.run {
                    // 设置标志，避免触发onChange检查
                    isProgrammaticallySettingUsername = true
                    username = generatedUsername
                    isProgrammaticallySettingUsername = false
                    print("🎲 使用SnowflakeUsernameGenerator异步生成唯一用户名: \(generatedUsername)")
                    // 生成后立即检查可用性
                    checkUsernameAvailability()
                }
            } catch {
                await MainActor.run {
                    print("❌ 异步生成用户名失败，回退到同步生成: \(error)")
                    generateAndFillUsername()
                }
            }
        }
    }

    private func isValidUsername(_ username: String) -> Bool {
        // 检查长度（至少5个字符）
        guard username.count >= 5 else { return false }

        // 检查字符（只允许字母和数字）
        let allowedCharacters = CharacterSet.alphanumerics
        let usernameCharacterSet = CharacterSet(charactersIn: username)
        guard allowedCharacters.isSuperset(of: usernameCharacterSet) else { return false }

        // 检查是否以字母开头
        guard let firstChar = username.first, firstChar.isLetter else { return false }

        // 检查是否在服务端黑名单中（与服务端保持一致）
        let serverBlacklist = ["admin"]
        guard !serverBlacklist.contains(username.lowercased()) else { return false }

        // 检查是否在本地不可用用户名列表中
        guard !Self.localUnavailableUsernames.contains(username.lowercased()) else { return false }

        return true
    }

    private func isValidEmail(_ email: String) -> Bool {
        let emailRegex = "[A-Z0-9a-z._%+-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,64}"
        let emailPredicate = NSPredicate(format:"SELF MATCHES %@", emailRegex)
        return emailPredicate.evaluate(with: email)
    }
}

// Preview暂时禁用，等待宏问题解决
// #Preview {
//     RegisterView()
// }


