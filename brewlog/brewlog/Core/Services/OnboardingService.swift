import Foundation
import Combine

/// 新手引导数据模型
struct OnboardingData: Codable {
    var beanName: String = ""
    var roasterType: String = ""
    var brewMethod: String = ""
    var grinderType: String = ""
}

/// 新手引导状态响应模型
struct OnboardingStatusResponse: Codable {
    let needsOnboarding: Bool
    
    enum CodingKeys: String, CodingKey {
        case needsOnboarding = "needs_onboarding"
    }
}

/// 新手引导提交响应模型
struct OnboardingSubmitResponse: Codable {
    let success: Bool
    let message: String
}

/// 新手引导服务
class OnboardingService: ObservableObject {
    static let shared = OnboardingService()
    
    private let apiService = APIService.shared
    var cancellables = Set<AnyCancellable>()
    
    private init() {}
    
    /// 检查用户是否需要新手引导
    /// - Returns: Publisher that emits whether onboarding is needed
    func checkOnboardingStatus() -> AnyPublisher<Bool, Error> {
        return Future { promise in
            Task {
                do {
                    let response: OnboardingStatusResponse = try await self.apiService.get("/ios/api/auth/onboarding/status/")
                    promise(.success(response.needsOnboarding))
                } catch {
                    promise(.failure(error))
                }
            }
        }
        .eraseToAnyPublisher()
    }
    
    /// 提交新手引导数据
    /// - Parameter data: 新手引导表单数据
    /// - Returns: Publisher that emits the submission result
    func submitOnboarding(data: OnboardingData) -> AnyPublisher<OnboardingSubmitResponse, Error> {
        return Future { promise in
            Task {
                do {
                    let requestBody = [
                        "beanName": data.beanName,
                        "roasterType": data.roasterType,
                        "brewMethod": data.brewMethod,
                        "grinderType": data.grinderType
                    ]

                    let response: OnboardingSubmitResponse = try await self.apiService.post("/ios/api/auth/onboarding/submit/", body: requestBody)
                    promise(.success(response))
                } catch {
                    promise(.failure(error))
                }
            }
        }
        .eraseToAnyPublisher()
    }
}

/// 新手引导选项数据
struct OnboardingOptions {
    /// 咖啡豆来源选项
    static let roasterTypes = [
        ("home", "自家烘焙"),
        ("commercial", "商业豆商")
    ]
    
    /// 冲煮方式选项
    static let brewMethods = [
        ("POUR_OVER", "手冲"),
        ("FRENCH_PRESS", "法压"),
        ("ESPRESSO", "意式浓缩"),
        ("AEROPRESS", "爱乐压"),
        ("COLD_BREW", "冷萃"),
        ("MOKA_POT", "摩卡壶"),
        ("SIPHON", "虹吸"),
        ("DRIP", "滴滤")
    ]
    
    /// 磨豆机类型选项
    static let grinderTypes = [
        ("dedicated", "专用磨豆机"),
        ("built_in", "研磨一体机"),
        ("store", "商家代磨")
    ]
}
