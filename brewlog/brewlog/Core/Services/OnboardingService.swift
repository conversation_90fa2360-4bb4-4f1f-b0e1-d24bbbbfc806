import Foundation
import Combine

/// 新手引导状态响应模型
struct OnboardingStatusResponse: Codable {
    let needsOnboarding: Bool

    enum CodingKeys: String, CodingKey {
        case needsOnboarding = "needs_onboarding"
    }
}

/// 新手引导服务
class OnboardingService: ObservableObject {
    static let shared = OnboardingService()
    
    private let apiService = APIService.shared
    var cancellables = Set<AnyCancellable>()
    
    private init() {}
    
    /// 检查用户是否需要新手引导
    /// - Returns: Publisher that emits whether onboarding is needed
    func checkOnboardingStatus() -> AnyPublisher<Bool, Error> {
        return Future { promise in
            Task {
                do {
                    let response: OnboardingStatusResponse = try await self.apiService.get("/ios/api/auth/onboarding/status/")
                    promise(.success(response.needsOnboarding))
                } catch {
                    promise(.failure(error))
                }
            }
        }
        .eraseToAnyPublisher()
    }
    
    /// 提交新手引导数据
    /// - Parameter data: 新手引导表单数据
    /// - Returns: Publisher that emits the submission result
    func submitOnboarding(data: OnboardingData) -> AnyPublisher<OnboardingSubmitResponse, Error> {
        return Future { promise in
            Task {
                do {
                    let requestBody = [
                        "beanName": data.beanName,
                        "roasterType": data.roasterType,
                        "brewMethod": data.brewMethod,
                        "grinderType": data.grinderType
                    ]

                    let response: OnboardingSubmitResponse = try await self.apiService.post("/ios/api/auth/onboarding/submit/", body: requestBody)
                    promise(.success(response))
                } catch {
                    promise(.failure(error))
                }
            }
        }
        .eraseToAnyPublisher()
    }
}
