import Foundation
import Combine
import SwiftUI

// MARK: - 本地模型定义
struct Bean: Identifiable, Codable {
    let id: Int
    let name: String
    let roaster: String
    let roastLevel: String
    let origin: String?
    let process: String?
    let description: String?
    let price: Double?
    let weight: Double?
    let purchaseDate: Date?
    let roastDate: Date?
    let isFinished: Bool
    
    enum CodingKeys: String, CodingKey {
        case id
        case name
        case roaster
        case roastLevel = "roast_level"
        case origin
        case process
        case description
        case price
        case weight
        case purchaseDate = "purchase_date"
        case roastDate = "roast_date"
        case isFinished = "is_finished"
    }
}

// BeanOccurrence结构体已在Models.swift中定义，此处移除
// 使用Models.swift中的BeanOccurrence结构体

struct BrewLog: Identifiable, Codable {
    let id: Int
    let recipeName: String?
    let beanId: Int
    let beanName: String
    let equipmentId: Int
    let equipmentName: String
    let grindSize: String?
    let doseWeight: Double
    let yieldWeight: Double?
    let waterTemperature: Double?
    let brewingTime: Int?
    let rating: Int
    let notes: String?
    let createdAt: Date
    
    enum CodingKeys: String, CodingKey {
        case id
        case recipeName = "recipe_name"
        case beanId = "bean_id"
        case beanName = "bean_name"
        case equipmentId = "equipment_id"
        case equipmentName = "equipment_name"
        case grindSize = "grind_size"
        case doseWeight = "dose_weight"
        case yieldWeight = "yield_weight"
        case waterTemperature = "water_temperature"
        case brewingTime = "brewing_time"
        case rating
        case notes
        case createdAt = "created_at"
    }
}

// MARK: - 简化设备类型（用于API）
typealias EquipmentData = [String: Any]

// MARK: - 自定义的日期解码策略
extension JSONDecoder {
    static func apiDecoder() -> JSONDecoder {
        let decoder = JSONDecoder()
        
        // 设置日期解码策略
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd'T'HH:mm:ss.SSSZ"
        
        decoder.dateDecodingStrategy = .custom { decoder in
            let container = try decoder.singleValueContainer()
            
            // 尝试不同的日期格式解码
            if let dateString = try? container.decode(String.self) {
                // 尝试ISO8601格式
                if let date = dateFormatter.date(from: dateString) {
                    return date
                }
                
                // 尝试其他常见日期格式...
                let alternateFormatters = [
                    "yyyy-MM-dd'T'HH:mm:ssZ",
                    "yyyy-MM-dd HH:mm:ss",
                    "yyyy-MM-dd"
                ]
                
                for format in alternateFormatters {
                    dateFormatter.dateFormat = format
                    if let date = dateFormatter.date(from: dateString) {
                        return date
                    }
                }
                
                throw DecodingError.dataCorruptedError(
                    in: container,
                    debugDescription: "无法解析日期字符串：\(dateString)"
                )
            } else if let timestamp = try? container.decode(Double.self) {
                // 处理Unix时间戳（以秒为单位）
                return Date(timeIntervalSince1970: timestamp)
            } else if let timestamp = try? container.decode(Int.self) {
                // 处理Unix时间戳（以秒为单位）
                print("解析时间戳：\(timestamp)")
                let date = Date(timeIntervalSince1970: TimeInterval(timestamp))
                print("解析为日期：\(date)")
                return date
            }
            
            throw DecodingError.dataCorruptedError(
                in: container,
                debugDescription: "日期值格式不正确"
            )
        }
        
        return decoder
    }
}

enum APIEnvironment {
    case development
    case production
    
    var baseURL: String {
        switch self {
        case .development:
            #if targetEnvironment(simulator)
            return "http://127.0.0.1:8000"  // 本机模拟器
            #else
            return "http://************:8000"  // 真机测试
            #endif
        case .production:
            return "https://kafeidazi.com"
        }
    }
    
    // 验证基础URL是否有效
    func validateBaseURL() -> Bool {
        guard let url = URL(string: baseURL) else {
            return false
        }
        return !url.absoluteString.isEmpty
    }
}

class APIService: ObservableObject {
    static let shared = APIService()
    // 临时强制使用开发环境进行新手引导功能测试
    static let environment: APIEnvironment = .development
    
    private var baseURL: String {
        APIService.environment.baseURL
    }
    private let session = URLSession.shared
    private var authToken: String?
    private var refreshToken: String?  // 添加刷新令牌
    private var csrfToken: String?
    private var sessionCookies: [HTTPCookie]?
    private var isRefreshing = false  // 标记是否正在刷新令牌
    private var refreshQueue: [() -> Void] = []  // 请求队列，用于在令牌刷新后重试
    
    // 钥匙串密钥
    private let tokenKey = "auth_token"
    private let refreshTokenKey = "refresh_token"
    private let deviceIdKey = "device_id"
    private let tokenExpiresAtKey = "token_expires_at"
    private let refreshTokenExpiresAtKey = "refresh_token_expires_at"
    
    // 令牌过期时间
    private var tokenExpiresAt: Date?
    private var refreshTokenExpiresAt: Date?
    
    // 令牌自动刷新定时器
    private var refreshTimer: Timer?
    
    // 令牌刷新时间间隔（18小时，比服务端令牌有效期24小时短）
    private let refreshInterval: TimeInterval = 18 * 60 * 60
    
    // 令牌刷新重试设置
    private let maxRefreshRetries = 3
    private var currentRefreshRetries = 0
    private let retryDelay: TimeInterval = 30 // 30秒后重试
    
    // 调试属性 - 记录最后一次响应数据
    private(set) var lastResponseData: Data?
    
    // 用于UI更新的属性
    @Published var beans: [Bean] = []
    @Published var brewLogs: [BrewLog] = []
    @Published var isLoading: Bool = false
    @Published var error: APIError? = nil
    @Published var isLoggedIn: Bool = false
    
    // 添加CSRF令牌获取锁
    private let csrfLock = NSLock()
    // 添加令牌验证锁
    private let tokenValidationLock = NSLock()
    // 添加一个处于获取CSRF中的标记
    private var isFetchingCSRF = false
    // 用于跟踪获取CSRF任务的actor
    private actor CSRFTaskManager {
        private var currentTask: Task<String, Error>?
        
        func getOrCreateTask() -> Task<String, Error> {
            if let task = currentTask {
                return task
            }
            
            let newTask = Task<String, Error> {
                // 结束时清除任务
                defer { Task { await self.clearTask() } }
                let result = try await APIService.shared.fetchCSRFTokenInternal()
                return result
            }
            
            currentTask = newTask
            return newTask
        }
        
        func clearTask() {
            currentTask = nil
        }
    }
    private let csrfTaskManager = CSRFTaskManager()
    
    private init() {
        #if ALLOW_INSECURE_HTTP
        if case .development = APIService.environment {
            URLSession.allowsInsecureRequests()
        }
        #endif
        
        // 验证 baseURL 的有效性
        if !APIService.environment.validateBaseURL() {
            #if DEBUG
            print("⚠️ 警告: 无效的 baseURL: \(baseURL)")
            fatalError("无效的 baseURL 配置，请检查 APIEnvironment 枚举")
            #else
            // 在发布版本中不触发 fatalError，而是记录错误
            print("⚠️ 严重错误: API 基础 URL 无效")
            #endif
        } else {
            #if DEBUG
            print("✅ API 基础 URL 有效: \(baseURL)")
            #endif
        }
        
        // 从钥匙串恢复认证状态
        restoreAuthState()
        
        // 添加应用进入后台和回到前台的通知监听
        setupAppStateNotifications()
    }
    
    // 从钥匙串恢复认证状态
    private func restoreAuthState() {
        do {
            // 尝试从钥匙串读取认证令牌
            self.authToken = try KeychainManager.load(key: tokenKey)
            
            // 尝试读取刷新令牌
            do {
                self.refreshToken = try KeychainManager.load(key: refreshTokenKey)
            } catch {
                #if DEBUG
                print("⚠️ 刷新令牌不可用: \(error.localizedDescription)")
                #endif
            }
            
            // 尝试读取过期时间
            if let expiresAtString = try? KeychainManager.load(key: tokenExpiresAtKey),
               let expiresAtTimestamp = Double(expiresAtString) {
                self.tokenExpiresAt = Date(timeIntervalSince1970: expiresAtTimestamp)
            }
            
            if let refreshExpiresAtString = try? KeychainManager.load(key: refreshTokenExpiresAtKey),
               let refreshExpiresAtTimestamp = Double(refreshExpiresAtString) {
                self.refreshTokenExpiresAt = Date(timeIntervalSince1970: refreshExpiresAtTimestamp)
            }
            
            // 更新登录状态
            self.isLoggedIn = self.authToken != nil
            
            #if DEBUG
            print("🔐 从钥匙串恢复认证状态：\(self.isLoggedIn ? "已登录" : "未登录")")
            if let expiresAt = self.tokenExpiresAt {
                let formatter = DateFormatter()
                formatter.dateStyle = .medium
                formatter.timeStyle = .medium
                print("📅 令牌过期时间: \(formatter.string(from: expiresAt))")
            }
            #endif
            
            // 如果已登录，启动令牌刷新定时器
            if isLoggedIn {
                scheduleTokenRefresh()
                
                // 应用启动时检查令牌有效性
                Task {
                    await checkTokenValidity()
                }
            }
        } catch {
            #if DEBUG
            print("⚠️ 从钥匙串恢复令牌失败: \(error.localizedDescription)")
            #endif
            
            // 恢复失败，清除可能的无效状态
            clearAuthToken()
        }
    }
    
    // 设置应用状态变化监听
    private func setupAppStateNotifications() {
        // 应用进入后台时
        NotificationCenter.default.addObserver(self, selector: #selector(appDidEnterBackground), name: UIApplication.didEnterBackgroundNotification, object: nil)
        
        // 应用回到前台时
        NotificationCenter.default.addObserver(self, selector: #selector(appWillEnterForeground), name: UIApplication.willEnterForegroundNotification, object: nil)
    }
    
    // 应用进入后台
    @objc private func appDidEnterBackground() {
        // 不再立即停止定时器，而是记录进入后台的时间
        // 这样应用回到前台时可以计算经过了多长时间
        UserDefaults.standard.set(Date().timeIntervalSince1970, forKey: "app_background_time")
        UserDefaults.standard.set(Date().timeIntervalSince1970, forKey: "last_token_used_time")
        
        #if DEBUG
        print("🔄 应用进入后台，记录时间戳")
        #endif
        
        // 后台处理时间有限，所以还是停止定时器
        refreshTimer?.invalidate()
        refreshTimer = nil
        
        // 避免并发执行令牌操作
        if tokenValidationLock.try() {
            // 在进入后台前，智能判断是否需要刷新令牌
            var shouldRefreshBeforeBackground = false
            
            // 如果令牌接近过期（12小时内），主动刷新一次
            if let expiresAt = tokenExpiresAt {
                let timeRemaining = expiresAt.timeIntervalSince(Date())
                
                if timeRemaining < 12 * 60 * 60 && timeRemaining > 0 {
                    shouldRefreshBeforeBackground = true
                }
            }
            
            // 如果刷新令牌接近过期（7天内），主动刷新一次
            if let refreshExpiresAt = refreshTokenExpiresAt {
                let refreshTimeRemaining = refreshExpiresAt.timeIntervalSince(Date())
                
                if refreshTimeRemaining < 7 * 24 * 60 * 60 {
                    shouldRefreshBeforeBackground = true
                }
            }
            
            // 如果上次刷新时间是3天前，也主动刷新一次
            let lastRefreshTime = UserDefaults.standard.double(forKey: "last_token_refresh_time")
            let timeSinceLastRefresh = Date().timeIntervalSince1970 - lastRefreshTime
            if timeSinceLastRefresh > 3 * 24 * 60 * 60 {
                shouldRefreshBeforeBackground = true
            }
            
            if shouldRefreshBeforeBackground {
                #if DEBUG
                print("⏱ 应用进入后台前主动刷新令牌")
                #endif
                
                // 在后台任务中执行令牌刷新
                let task = UIApplication.shared.beginBackgroundTask { [weak self] in
                    self?.stopBackgroundRefreshIfNeeded()
                    self?.tokenValidationLock.unlock() // 确保锁被释放
                }
                
                if task != .invalid {
                    // 异步执行令牌刷新
                    Task {
                        do {
                            try await refreshAuthToken()
                            self.stopBackgroundRefreshIfNeeded(task: task)
                            self.tokenValidationLock.unlock() // 处理完成后释放锁
                        } catch {
                            #if DEBUG
                            print("❌ 后台刷新令牌失败: \(error.localizedDescription)")
                            #endif
                            self.stopBackgroundRefreshIfNeeded(task: task)
                            self.tokenValidationLock.unlock() // 失败时也要释放锁
                        }
                    }
                } else {
                    // 如果无法创建后台任务，也要释放锁
                    tokenValidationLock.unlock()
                }
            } else {
                // 如果不需要刷新，释放锁
                tokenValidationLock.unlock()
            }
        } else {
            #if DEBUG
            print("🔒 后台进入时无法获取令牌验证锁，跳过令牌检查")
            #endif
        }
    }
    
    // 结束后台刷新任务
    private func stopBackgroundRefreshIfNeeded(task: UIBackgroundTaskIdentifier? = nil) {
        if let task = task, task != .invalid {
            UIApplication.shared.endBackgroundTask(task)
        }
    }
    
    // 应用回到前台
    @objc private func appWillEnterForeground() {
        // 如果已登录，验证令牌并根据需要重新启动定时器
        if isLoggedIn {
            // 检查应用在后台停留了多长时间
            let lastBackgroundTime = UserDefaults.standard.double(forKey: "app_background_time")
            let currentTime = Date().timeIntervalSince1970
            let backgroundDuration = currentTime - lastBackgroundTime
            
            #if DEBUG
            if lastBackgroundTime > 0 {
                print("🕒 应用在后台停留了 \(Int(backgroundDuration/60)) 分钟")
            }
            #endif
            
            // 如果后台停留时间超过6小时，立即执行令牌验证
            let shouldVerifyImmediately = backgroundDuration > 6 * 60 * 60
            
            // 创建一个延迟任务检查令牌，给应用UI启动一些时间
            DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                Task {
                    #if DEBUG
                    print("🔄 应用回到前台，\(shouldVerifyImmediately ? "立即" : "")检查令牌有效性")
                    #endif
                    
                    // 检查令牌有效性
                    await self.checkTokenValidity(forceCheck: shouldVerifyImmediately)
                }
            }
        }
    }
    
    // 检查令牌是否有效
    func checkTokenValidity(forceCheck: Bool = false) async {
        guard self.authToken != nil else {
            // 没有令牌，不需要验证
            clearAuthToken()
            return
        }
        
        // 使用锁防止并发验证
        guard tokenValidationLock.try() else {
            #if DEBUG
            print("🔒 另一个令牌验证过程已在进行中，跳过此次验证")
            #endif
            return
        }
        
        // 确保在函数退出时释放锁
        defer {
            tokenValidationLock.unlock()
        }
        
        #if DEBUG
        print("🔍 检查令牌有效性...")
        #endif
        
        // 检查在应用启动时，是否需要立即刷新令牌（基于上次访问时间）
        let lastUsedTime = UserDefaults.standard.double(forKey: "last_token_used_time")
        let currentTime = Date().timeIntervalSince1970
        let unusedDuration = currentTime - lastUsedTime
        
        // 如果超过7天未使用应用，强制刷新令牌
        let forceRefreshDueToInactivity = lastUsedTime > 0 && unusedDuration > 7 * 24 * 60 * 60
        
        #if DEBUG
        if forceRefreshDueToInactivity {
            print("⚠️ 应用\(Int(unusedDuration/60/60/24))天未使用，强制刷新令牌")
        }
        #endif
        
        // 更新最后使用时间
        UserDefaults.standard.set(currentTime, forKey: "last_token_used_time")
        
        // 检查令牌是否接近过期（如果过期时间可用）
        if let expiresAt = tokenExpiresAt {
            let now = Date()
            let timeInterval = expiresAt.timeIntervalSince(now)
            
            #if DEBUG
            print("⏱ 令牌剩余有效时间: \(Int(timeInterval))秒")
            #endif
            
            // 如果令牌已过期或即将过期（小于6小时），或者强制检查，或者长时间未使用，尝试刷新
            if timeInterval < 6 * 60 * 60 || forceCheck || forceRefreshDueToInactivity {
                #if DEBUG
                let reason = forceCheck ? "强制检查" : (forceRefreshDueToInactivity ? "长时间未使用" : "即将过期")
                print("⚠️ 刷新令牌（原因：\(reason)，剩余\(Int(timeInterval/60/60))小时）...")
                #endif
                
                do {
                    try await refreshAuthToken()
                    return
                } catch {
                    #if DEBUG
                    print("❌ 刷新令牌失败: \(error.localizedDescription)")
                    #endif
                    
                    // 启动重试机制
                    currentRefreshRetries = 0
                    retryRefreshAuthToken()
                    return
                }
            }
        } else if forceCheck || forceRefreshDueToInactivity {
            // 如果没有过期时间信息但需要强制检查，直接刷新令牌
            do {
                try await refreshAuthToken()
                return
            } catch {
                #if DEBUG
                print("❌ 强制刷新令牌失败: \(error.localizedDescription)")
                #endif
                
                // 启动重试机制
                currentRefreshRetries = 0
                retryRefreshAuthToken()
                return
            }
        } else {
            // 为刷新令牌设置更长的考虑期（比access token早15天过期）
            if let refreshExpiresAt = refreshTokenExpiresAt {
                let refreshTimeRemaining = refreshExpiresAt.timeIntervalSince(Date())
                
                // 如果刷新令牌还有不到15天过期，也尝试刷新
                if refreshTimeRemaining < 15 * 24 * 60 * 60 {
                    #if DEBUG
                    print("⚠️ 刷新令牌即将过期（剩余\(Int(refreshTimeRemaining/60/60/24))天），提前刷新")
                    #endif
                    
                    do {
                        try await refreshAuthToken()
                        return
                    } catch {
                        #if DEBUG
                        print("❌ 刷新令牌失败: \(error.localizedDescription)")
                        #endif
                    }
                }
            }
        }
        
        // 如果没有过期时间信息或令牌仍然有效，进行令牌验证
        do {
            let deviceInfo = getDeviceInfo()
            try await verifyToken(deviceId: deviceInfo["device_id"])
            
            // 令牌有效，重新启动刷新定时器
            scheduleTokenRefresh()
            
            #if DEBUG
            print("✅ 令牌验证成功，重新启动刷新定时器")
            #endif
        } catch {
            #if DEBUG
            print("❌ 令牌验证失败: \(error.localizedDescription)")
            #endif
            
            // 如果验证失败，尝试刷新令牌
            do {
                try await refreshAuthToken()
                #if DEBUG
                print("✅ 验证失败后刷新令牌成功")
                #endif
            } catch {
                #if DEBUG
                print("❌ 验证失败后刷新令牌也失败: \(error.localizedDescription)")
                #endif
                
                // 启动重试机制
                currentRefreshRetries = 0
                retryRefreshAuthToken()
            }
        }
    }
    
    // 验证当前令牌
    private func verifyToken(deviceId: String?) async throws {
        guard self.authToken != nil else {
            throw APIError.unauthorized
        }
        
        // 构建请求体
        var body: [String: Any] = [:]
        if let deviceId = deviceId {
            body["device_id"] = deviceId
        }
        
        #if DEBUG
        print("🔐 验证令牌...")
        #endif
        
        // 发送验证请求
        do {
            // 使用TokenVerifyResponse类型替代[String: Any]
            let response: TokenVerifyResponse = try await post("/ios/api/auth/token/verify/", body: body)
            if response.valid {
                #if DEBUG
                print("✅ 令牌有效")
                #endif
                return
            } else {
                throw APIError.unauthorized
            }
        } catch {
            #if DEBUG
            print("❌ 令牌验证失败: \(error.localizedDescription)")
            #endif
            throw error
        }
    }
    
    // 辅助函数 - 解析JSON数据
    private func parseJSONData<T: Decodable>(_ data: Data) throws -> T {
        // 记录数据类型信息，帮助调试
        var jsonType = "未知类型"
        var jsonTopLevelKeys: [String] = []
        
        do {
            // 先尝试分析JSON数据结构
            if let jsonObject = try? JSONSerialization.jsonObject(with: data, options: []) {
                if let dictionary = jsonObject as? [String: Any] {
                    jsonType = "__NSDictionaryI"
                    jsonTopLevelKeys = Array(dictionary.keys)
                    
                    // 打印JSON结构相关信息
                    print("📊 JSON数据类型: \(jsonType)")
                    print("📊 JSON顶级键: \(jsonTopLevelKeys.joined(separator: ", "))")
                    
                    // 检查是否包含'results'字段，这通常表示标准分页响应
                    if dictionary["results"] != nil {
                        print("🔍 检测到'results'键，可能是标准分页响应格式")
                        
                        // 提供部分JSON内容预览
                        if let jsonData = try? JSONSerialization.data(withJSONObject: dictionary, options: [.fragmentsAllowed, .prettyPrinted]) {
                            if let previewString = String(data: jsonData, encoding: .utf8) {
                                let shortened = previewString.prefix(200) + (previewString.count > 200 ? "..." : "")
                                print("📦 JSON数据预览: \(shortened)")
                            }
                        }
                    }
                } else if jsonObject is [Any] {
                    jsonType = "__NSArrayI"
                    print("📊 JSON数据类型: \(jsonType) (数组)")
                }
            }
            
            // 首先尝试直接解析为目标类型
            let decoder = JSONDecoder()
            decoder.keyDecodingStrategy = .useDefaultKeys
            decoder.dateDecodingStrategy = .secondsSince1970
            
            return try decoder.decode(T.self, from: data)
        } catch {
            print("❌ JSON解析错误: \(error)")
            if let decodingError = error as? DecodingError {
                switch decodingError {
                case .typeMismatch(let type, let context):
                    print("📌 类型不匹配: 期望类型 \(type), 路径: \(context.codingPath.map { $0.stringValue }.joined(separator: ".")), 描述: \(context.debugDescription)")
                    
                    // 对于数组类型不匹配，检查是否可以从字典的results字段中提取
                    if type is [Any].Type || String(describing: type).contains("Array") {
                        if jsonType == "__NSDictionaryI" && jsonTopLevelKeys.contains("results") {
                            print("🔍 检测到数组类型不匹配，尝试从字典的results字段中提取")
                            
                            // 使用泛型包装器解析results字段
                            let jsonDecoder = JSONDecoder()
                            jsonDecoder.keyDecodingStrategy = .useDefaultKeys
                            jsonDecoder.dateDecodingStrategy = .secondsSince1970
                            
                            if let wrappedResults = try? jsonDecoder.decode(ResultsWrapper<T>.self, from: data) {
                                return wrappedResults.results
                            }
                        }
                    }
                case .valueNotFound(let type, let context):
                    print("📌 未找到值: 期望类型 \(type), 路径: \(context.codingPath.map { $0.stringValue }.joined(separator: ".")), 描述: \(context.debugDescription)")
                case .keyNotFound(let key, let context):
                    print("📌 未找到键: \(key.stringValue), 路径: \(context.codingPath.map { $0.stringValue }.joined(separator: ".")), 描述: \(context.debugDescription)")
                case .dataCorrupted(let context):
                    print("📌 数据损坏: 路径: \(context.codingPath.map { $0.stringValue }.joined(separator: ".")), 描述: \(context.debugDescription)")
                @unknown default:
                    print("📌 未知解码错误: \(decodingError)")
                }
            }
            
            throw APIError.decodingError(error)
        }
    }
    
    // 辅助函数 - 检查令牌是否过期
    private func isTokenExpired(_ data: Data) -> Bool {
        // 简化流程，避免多重数据转换
        do {
            // 直接尝试解析 JSON 数据
            if let json = try JSONSerialization.jsonObject(with: data, options: []) as? [String: Any] {
                // 检查常见的令牌过期错误代码
                if let code = json["code"] as? String, code == "token_not_valid" {
                    return true
                }
                
                // 检查详细信息中的错误代码
                if let detail = json["detail"] as? [String: Any], 
                   let code = detail["code"] as? String, 
                   code == "token_not_valid" {
                    return true
                }
                
                // 检查错误详情中的提示
                if let detail = json["detail"] as? String, 
                   detail.contains("token is invalid") || detail.contains("token not valid") {
                    return true
                }
            }
            return false
        } catch {
            #if DEBUG
            print("⚠️ 解析令牌过期响应失败: \(error.localizedDescription)")
            if let responseString = String(data: data, encoding: .utf8) {
                print("📝 原始响应: \(responseString)")
            }
            #endif
            return false
        }
    }
    
    // 获取当前用户信息
    func getCurrentUserProfile() async throws -> User {
        return try await get("/ios/api/user/profile/")
    }
    
    // 获取当前用户的设备列表
    func getUserDevices() async throws -> [UserDevice] {
        let response: UserDevicesResponse = try await get("/ios/api/user/devices/")
        return response.devices
    }
    
    // 验证访问令牌是否有效
    func verifyToken() async throws -> TokenVerifyResponse {
        return try await get("/ios/api/auth/token/verify/")
    }
    
    // 更新用户昵称
    func updateNickname(nickname: String) async throws -> Bool {
        guard !nickname.isEmpty else {
            throw APIError.serverError(400, "昵称不能为空")
        }
        
        guard nickname.count <= 12 else {
            throw APIError.serverError(400, "昵称长度不能超过12个字符")
        }
        
        // 确保先获取CSRF token
        _ = try await fetchCSRFToken()
        
        let body = ["first_name": nickname]
        let response: ProfileUpdateResponse = try await put("/ios/api/user/profile/", body: body)
        
        // 验证是否成功更新
        if response.success {
            return true
        } else {
            throw APIError.serverError(500, response.message ?? "昵称更新失败")
        }
    }
    
    // 安排令牌刷新的定时器
    private func scheduleTokenRefresh() {
        // 首先取消任何现有的定时器
        refreshTimer?.invalidate()
        
        // 创建新的定时器，根据refreshInterval定期刷新令牌
        refreshTimer = Timer.scheduledTimer(withTimeInterval: refreshInterval, repeats: true) { [weak self] _ in
            guard let self = self else { return }
            
            #if DEBUG
            print("🔄 定时器触发令牌刷新")
            #endif
            
            // 异步执行令牌刷新
            Task {
                do {
                    try await self.refreshAuthToken()
                } catch {
                    #if DEBUG
                    print("❌ 定时刷新令牌失败: \(error.localizedDescription)")
                    #endif
                    
                    // 重置重试计数器（定时器触发的刷新单独计数）
                    self.currentRefreshRetries = 0
                    
                    // 尝试重试刷新令牌
                    self.retryRefreshAuthToken()
                }
            }
        }
        
        #if DEBUG
        // 修改显示时间间隔的计算方式，避免显示0天
        let days = Int(refreshInterval/3600/24)
        let hours = Int(refreshInterval/3600) % 24
        if days > 0 {
            print("⏰ 设置令牌刷新定时器，间隔: \(days)天\(hours > 0 ? " \(hours)小时" : "")")
        } else {
            print("⏰ 设置令牌刷新定时器，间隔: \(hours)小时")
        }
        #endif
    }
    
    // 重试刷新令牌
    private func retryRefreshAuthToken() {
        guard currentRefreshRetries < maxRefreshRetries else {
            #if DEBUG
            print("⚠️ 令牌刷新重试次数已达上限(\(maxRefreshRetries)次)，放弃重试")
            #endif
            currentRefreshRetries = 0
            return
        }
        
        currentRefreshRetries += 1
        
        #if DEBUG
        print("🔄 准备重试刷新令牌 (第\(currentRefreshRetries)次尝试)，\(retryDelay)秒后执行")
        #endif
        
        // 延迟一段时间后重试
        DispatchQueue.main.asyncAfter(deadline: .now() + retryDelay) {
            Task { [weak self] in
                guard let self = self else { return }
                
                do {
                    try await self.refreshAuthToken()
                    // 刷新成功，重置重试计数
                    self.currentRefreshRetries = 0
                    
                    #if DEBUG
                    print("✅ 令牌刷新重试成功")
                    #endif
                } catch {
                    #if DEBUG
                    print("❌ 令牌刷新重试失败: \(error.localizedDescription)")
                    #endif
                    
                    // 继续重试
                    self.retryRefreshAuthToken()
                }
            }
        }
    }
    
    // 停止令牌刷新定时器
    private func stopTokenRefreshTimer() {
        refreshTimer?.invalidate()
        refreshTimer = nil
        
        #if DEBUG
        print("⏰ 停止令牌刷新定时器")
        #endif
    }
    
    // 获取设备唯一标识符
    private func getDeviceID() -> String {
        // 尝试从钥匙串读取设备ID
        if let deviceID = try? KeychainManager.load(key: deviceIdKey) {
            return deviceID
        }
        
        // 否则获取新的设备ID并保存到钥匙串
        let deviceID = UIDevice.current.identifierForVendor?.uuidString ?? UUID().uuidString
        
        do {
            try KeychainManager.save(key: deviceIdKey, value: deviceID)
            #if DEBUG
            print("📱 生成并保存设备ID到钥匙串: \(deviceID)")
            #endif
        } catch {
            #if DEBUG
            print("⚠️ 无法保存设备ID到钥匙串: \(error.localizedDescription)")
            #endif
        }
        
        return deviceID
    }
    
    // 获取设备信息
    private func getDeviceInfo() -> [String: String] {
        return [
            "device_id": getDeviceID(),
            "device_model": UIDevice.current.model,
            "device_os_version": UIDevice.current.systemVersion,
            "app_version": Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? "Unknown"
        ]
    }
    
    func setAuthToken(_ token: String, refreshToken: String? = nil, expiresIn: Int? = nil, refreshExpiresIn: Int? = nil) {
        self.authToken = token
        self.isLoggedIn = true
        
        // 保存令牌到钥匙串
        do {
            try KeychainManager.save(key: tokenKey, value: token)
            
            // 如果提供了过期时间，计算过期时间点并保存
            if let expiresIn = expiresIn {
                let expiresAt = Date().addingTimeInterval(TimeInterval(expiresIn))
                self.tokenExpiresAt = expiresAt
                try KeychainManager.save(key: tokenExpiresAtKey, value: String(expiresAt.timeIntervalSince1970))
                
                #if DEBUG
                let formatter = DateFormatter()
                formatter.dateStyle = .medium
                formatter.timeStyle = .medium
                print("📅 保存访问令牌过期时间: \(formatter.string(from: expiresAt))")
                #endif
            }
            
            // 如果提供了刷新令牌，也保存它
            if let refreshToken = refreshToken {
                self.refreshToken = refreshToken
                try KeychainManager.save(key: refreshTokenKey, value: refreshToken)
                
                // 如果提供了刷新令牌过期时间，计算过期时间点并保存
                if let refreshExpiresIn = refreshExpiresIn {
                    let refreshExpiresAt = Date().addingTimeInterval(TimeInterval(refreshExpiresIn))
                    self.refreshTokenExpiresAt = refreshExpiresAt
                    try KeychainManager.save(key: refreshTokenExpiresAtKey, value: String(refreshExpiresAt.timeIntervalSince1970))
                    
                    #if DEBUG
                    let formatter = DateFormatter()
                    formatter.dateStyle = .medium
                    formatter.timeStyle = .medium
                    print("📅 保存刷新令牌过期时间: \(formatter.string(from: refreshExpiresAt))")
                    #endif
                }
            }
            
            #if DEBUG
            print("✅ 令牌已安全保存到钥匙串")
            #endif
        } catch {
            #if DEBUG
            print("❌ 保存令牌到钥匙串失败: \(error.localizedDescription)")
            #endif
        }
        
        // 启动令牌刷新定时器
        scheduleTokenRefresh()
    }
    
    func clearAuthToken() {
        self.authToken = nil
        self.refreshToken = nil
        self.csrfToken = nil
        self.sessionCookies = nil
        self.isLoggedIn = false
        self.tokenExpiresAt = nil
        self.refreshTokenExpiresAt = nil
        
        // 停止令牌刷新定时器
        stopTokenRefreshTimer()
        
        // 从钥匙串中移除
        do {
            try KeychainManager.delete(key: tokenKey)
            try KeychainManager.delete(key: refreshTokenKey)
            try KeychainManager.delete(key: tokenExpiresAtKey)
            try KeychainManager.delete(key: refreshTokenExpiresAtKey)
            // 不删除设备ID，保持唯一性
            
            #if DEBUG
            print("🗑 已从钥匙串清除所有令牌信息")
            #endif
        } catch {
            #if DEBUG
            print("⚠️ 从钥匙串删除令牌时出错: \(error.localizedDescription)")
            #endif
        }
    }
    
    // 修改原来的fetchCSRFToken为一个内部方法
    private func fetchCSRFTokenInternal() async throws -> String {
        let tokenResponse: [String: String] = try await get("/ios/api/csrf-token/")
        guard let token = tokenResponse["csrfToken"] else {
            throw APIError.serverError(400, "无法获取CSRF Token")
        }
        self.csrfToken = token
        return token
    }
    
    // 提供一个新的公共方法，使用任务复用机制
    func fetchCSRFToken() async throws -> String {
        if let existingToken = self.csrfToken {
            #if DEBUG
            print("🔑 使用已有的CSRF令牌: \(existingToken.prefix(10))...")
            #endif
            return existingToken
        }
        
        // 使用Actor管理任务，确保只有一个获取CSRF的网络请求
        let task = await csrfTaskManager.getOrCreateTask()
        return try await task.value
    }
    
    func login(username: String, password: String) async throws -> LoginResponse {
        // 确保先获取CSRF token
        _ = try await fetchCSRFToken()

        // 获取设备信息
        let deviceInfo = getDeviceInfo()

        // 构建请求体，包含设备信息
        var body: [String: Any] = [
            "username": username,
            "password": password
        ]

        // 添加所有设备信息
        for (key, value) in deviceInfo {
            body[key] = value
        }

        #if DEBUG
        print("🔑 登录请求包含设备信息: \(deviceInfo)")
        #endif

        let response: LoginResponse = try await post("/ios/api/auth/login/", body: body)

        // 保存认证令牌和刷新令牌，包括过期时间
        setAuthToken(
            response.access,
            refreshToken: response.refresh,
            expiresIn: response.expiresIn,
            refreshExpiresIn: response.refreshExpiresIn
        )

        return response
    }

    func register(username: String, email: String, password: String, firstName: String? = nil) async throws -> RegisterResponse {
        // 确保先获取CSRF token
        _ = try await fetchCSRFToken()

        // 获取设备信息
        let deviceInfo = getDeviceInfo()

        // 构建请求体，包含设备信息
        var body: [String: Any] = [
            "username": username,
            "email": email,
            "password": password
        ]

        if let firstName = firstName, !firstName.isEmpty {
            body["first_name"] = firstName
        }

        // 添加所有设备信息
        for (key, value) in deviceInfo {
            body[key] = value
        }

        #if DEBUG
        print("📝 注册请求包含设备信息: \(deviceInfo)")
        #endif

        let response: RegisterResponse = try await post("/ios/api/auth/register/", body: body)

        // 保存认证令牌和刷新令牌，包括过期时间
        setAuthToken(
            response.access,
            refreshToken: response.refresh,
            expiresIn: response.expiresIn,
            refreshExpiresIn: response.refreshExpiresIn
        )

        return response
    }

    func resetPassword(email: String) async throws -> PasswordResetResponse {
        // 确保先获取CSRF token
        _ = try await fetchCSRFToken()

        let body: [String: Any] = [
            "email": email
        ]

        #if DEBUG
        print("🔄 密码重置请求: \(email)")
        #endif

        let response: PasswordResetResponse = try await post("/ios/api/auth/password/reset/", body: body)
        return response
    }

    // 检查用户名可用性
    func checkUsernameAvailability(username: String) async throws -> UsernameAvailabilityResponse {
        let parameters = ["username": username]

        #if DEBUG
        print("🔍 检查用户名可用性: \(username)")
        #endif

        let response: UsernameAvailabilityResponse = try await get("/ios/api/auth/check-username/", parameters: parameters)
        return response
    }
    
    func logout() async throws -> Bool {
        guard let refreshToken = self.refreshToken else {
            // 如果没有刷新令牌，直接清除本地令牌
            clearAuthToken()
            return true
        }
        
        do {
            // 获取设备ID
            let deviceID = getDeviceID()
            
            // 将刷新令牌加入黑名单
            var body: [String: Any] = ["refresh": refreshToken]
            
            // 添加设备ID
            body["device_id"] = deviceID
            
            // 将设备加入黑名单（安全登出）
            body["blacklist_device"] = false
            
            let _: [String: Bool] = try await post("/ios/api/auth/token/blacklist/", body: body)
            
            // 清除本地令牌
            clearAuthToken()
            return true
        } catch {
            // 即使服务器端操作失败，也清除本地令牌
            clearAuthToken()
            throw error
        }
    }
    
    // 刷新认证令牌
    func refreshAuthToken() async throws {
        // 如果已经在刷新，返回
        if isRefreshing {
            #if DEBUG
            print("⏳ 令牌刷新已在进行中...")
            #endif
            return
        }
        
        guard let refreshToken = self.refreshToken else {
            #if DEBUG
            print("❌ 没有刷新令牌可用")
            #endif
            throw APIError.unauthorized
        }
        
        // 检查刷新令牌是否已过期
        if let refreshExpiresAt = self.refreshTokenExpiresAt, refreshExpiresAt < Date() {
            #if DEBUG
            print("⚠️ 刷新令牌已过期，需要重新登录")
            #endif
            // 清除已过期的所有令牌
            clearAuthToken()
            throw APIError.unauthorized
        }
        
        // 标记开始刷新
        isRefreshing = true
        
        // 使用defer确保无论成功或失败，都会重置刷新状态
        defer {
            isRefreshing = false
        }
        
        #if DEBUG
        print("🔄 开始刷新令牌...")
        #endif
        
        do {
            // 获取设备信息
            let deviceInfo = getDeviceInfo()
            
            // 构建请求体
            var body: [String: Any] = ["refresh": refreshToken]
            
            // 添加设备信息
            for (key, value) in deviceInfo {
                body[key] = value
            }
            
            // 发送刷新请求
            let response: RefreshResponse = try await post("/ios/api/auth/token/refresh/", body: body)
            
            // 更新令牌
            self.authToken = response.access
            if let refresh = response.refresh {
                self.refreshToken = refresh
            }
            
            // 更新钥匙串中的令牌
            try KeychainManager.save(key: tokenKey, value: response.access)
            if let refresh = response.refresh {
                try KeychainManager.save(key: refreshTokenKey, value: refresh)
            }
            
            // 更新过期时间
            if let expiresIn = response.expiresIn {
                let expiresAt = Date().addingTimeInterval(TimeInterval(expiresIn))
                self.tokenExpiresAt = expiresAt
                try KeychainManager.save(key: tokenExpiresAtKey, value: String(expiresAt.timeIntervalSince1970))
                
                // 估算刷新令牌过期时间（未返回时）- 保守估计为28天
                if response.refresh != nil && self.refreshTokenExpiresAt == nil {
                    let refreshExpiresAt = Date().addingTimeInterval(28 * 24 * 60 * 60)
                    self.refreshTokenExpiresAt = refreshExpiresAt
                    try KeychainManager.save(key: refreshTokenExpiresAtKey, value: String(refreshExpiresAt.timeIntervalSince1970))
                }
                
                #if DEBUG
                let formatter = DateFormatter()
                formatter.dateStyle = .medium
                formatter.timeStyle = .medium
                print("📅 更新访问令牌过期时间: \(formatter.string(from: expiresAt))")
                if let refreshExpiresAt = self.refreshTokenExpiresAt {
                    print("📅 刷新令牌过期时间: \(formatter.string(from: refreshExpiresAt))")
                }
                #endif
            }
            
            // 更新最后刷新时间
            UserDefaults.standard.set(Date().timeIntervalSince1970, forKey: "last_token_refresh_time")
            
            #if DEBUG
            print("✅ 令牌刷新成功")
            #endif
            
            // 重置重试计数
            currentRefreshRetries = 0
            
            // 执行所有等待中的请求
            let retryQueue = self.refreshQueue
            self.refreshQueue = []
            retryQueue.forEach { $0() }
            
        } catch {
            #if DEBUG
            print("❌ 令牌刷新失败: \(error.localizedDescription)")
            #endif
            
            // 不立即清除令牌，交给重试机制处理
            throw error
        }
    }
    
    // 用于刷新令牌的响应模型
    struct RefreshResponse: Codable {
        let access: String
        let refresh: String?
        let expiresIn: Int?
        
        enum CodingKeys: String, CodingKey {
            case access
            case refresh
            case expiresIn = "expires_in"
        }
    }
    
    private func addCookies(to request: inout URLRequest) {
        if let cookies = sessionCookies, !cookies.isEmpty {
            let cookieHeaders = HTTPCookie.requestHeaderFields(with: cookies)
            for (field, value) in cookieHeaders {
                request.setValue(value, forHTTPHeaderField: field)
            }
        }
    }
    
    private func saveCookies(from response: HTTPURLResponse, for url: URL) {
        if let headerFields = response.allHeaderFields as? [String: String] {
            let cookies = HTTPCookie.cookies(withResponseHeaderFields: headerFields, for: url)
            if !cookies.isEmpty {
                sessionCookies = cookies
            }
        }
    }
    
    // 恢复 get 方法
    func get<T: Decodable>(_ endpoint: String, parameters: [String: String] = [:]) async throws -> T {
        // 尝试正常请求
        do {
            // 安全创建URL组件，避免强制解包
            guard let urlComponents = URLComponents(string: baseURL + endpoint) else {
                print("❌ 无法创建URL组件，基础URL：\(baseURL)，端点：\(endpoint)")
                throw APIError.invalidURL
            }
            
            var components = urlComponents
            
            // 添加URL参数
            if !parameters.isEmpty {
                components.queryItems = parameters.map { URLQueryItem(name: $0.key, value: $0.value) }
            }
            
            // 获取完整URL，如果失败则使用回退方案
            let fullUrl = components.url?.absoluteString ?? (baseURL + endpoint)
            
            // 使用完整URL直接调用executeRequest
            return try await executeRequest(endpoint: fullUrl, method: "GET")
        } catch APIError.unauthorized {
            // 如果是未授权错误，尝试刷新令牌并重试请求
            if self.refreshToken != nil {
                do {
                    await checkTokenValidity()
                    
                    // 安全创建URL组件，避免强制解包
                    guard let urlComponents = URLComponents(string: baseURL + endpoint) else {
                        print("❌ 刷新令牌后无法创建URL组件，基础URL：\(baseURL)，端点：\(endpoint)")
                        throw APIError.invalidURL
                    }
                    
                    var components = urlComponents
                    
                    if !parameters.isEmpty {
                        components.queryItems = parameters.map { URLQueryItem(name: $0.key, value: $0.value) }
                    }
                    
                    // 获取完整URL，如果失败则使用回退方案
                    let fullUrl = components.url?.absoluteString ?? (baseURL + endpoint)
                    
                    return try await executeRequest(endpoint: fullUrl, method: "GET")
                } catch {
                    // 如果刷新失败，则抛出原始未授权错误
                    throw APIError.unauthorized
                }
            } else {
                // 没有刷新令牌，直接抛出未授权错误
                throw APIError.unauthorized
            }
        }
    }
    
    // 修改executeRequest方法，优化CSRF令牌获取逻辑
    private func executeRequest<T: Decodable>(endpoint: String, method: String, body: [String: Any]? = nil) async throws -> T {
        // 对于需要CSRF的方法，确保获取CSRF令牌
        if (method == "POST" || method == "PUT" || method == "DELETE") && csrfToken == nil && !endpoint.contains("/csrf-token/") {
            #if DEBUG
            print("🔑 执行请求前获取CSRF令牌...")
            #endif
            _ = try await fetchCSRFToken()
        }
        
        // 安全处理URL
        guard let rawUrl = URL(string: endpoint.hasPrefix("http") ? endpoint : baseURL + endpoint) else {
            print("❌ executeRequest中无法创建URL，输入: \(endpoint)")
            throw APIError.invalidURL
        }
        
        // 规范化URL，避免多余的斜杠问题
        let urlString = rawUrl.absoluteString.replacingOccurrences(of: "([^:])//+", with: "$1/", options: .regularExpression)
        guard let url = URL(string: urlString) else {
            print("❌ executeRequest中无法规范化URL: \(urlString)")
            throw APIError.invalidURL
        }
        
        // 打印最终使用的URL
        print("📡 最终请求URL: \(url.absoluteString)")
        
        var request = URLRequest(url: url)
        request.httpMethod = method
        
        // 设置通用header
        request.setValue("application/json", forHTTPHeaderField: "Content-Type")
        request.setValue("application/json", forHTTPHeaderField: "Accept")
        
        // 添加认证令牌(如果存在)
        if let token = authToken {
            request.setValue("Bearer \(token)", forHTTPHeaderField: "Authorization")
        }
        
        // 添加CSRF令牌(如果存在)
        if let csrf = csrfToken {
            request.setValue(csrf, forHTTPHeaderField: "X-CSRFToken")
            print("🔑 添加CSRF令牌到请求头: \(csrf.prefix(10))...")
        } else if method == "POST" || method == "PUT" || method == "DELETE" {
            print("⚠️ 警告: 执行\(method)请求时没有CSRF令牌")
        }
        
        #if !DEBUG
        // 在请求中添加设备标识
        request.setValue(getDeviceID(), forHTTPHeaderField: "X-Device-ID")
        #endif
        
        // 添加请求体(如果存在)
        if let body = body {
            do {
                request.httpBody = try JSONSerialization.data(withJSONObject: body)
            } catch {
                #if DEBUG
                print("❌ 序列化请求体失败: \(error)")
                #endif
                throw APIError.invalidResponse
            }
        }
        
        // 添加所有cookies(如果存在)
        if let cookies = sessionCookies {
            let cookieStore = HTTPCookieStorage.shared
            cookieStore.setCookies(cookies, for: url, mainDocumentURL: nil)
        }
        
        #if DEBUG
        // 调试日志: 打印请求详情
        print("📤 \(method) \(url)")
        request.allHTTPHeaderFields?.forEach { field, value in
            if field != "Authorization" { // 不打印敏感信息
                print("🔤 Header: \(field): \(value)")
            } else {
                print("🔤 Header: \(field): [已隐藏]")
            }
        }
        if let body = body, !body.isEmpty {
            // 打印请求体，但隐藏敏感信息
            var safePrintBody = body
            if safePrintBody["password"] != nil {
                safePrintBody["password"] = "***" // 隐藏密码
            }
            if safePrintBody["refresh"] != nil {
                safePrintBody["refresh"] = "***" // 隐藏刷新令牌
            }
            print("📦 Request Body: \(safePrintBody)")
        }
        #endif
        
        do {
            // 设置超时
            request.timeoutInterval = 15.0 // 15秒超时
            
            let (data, response) = try await session.data(for: request)
            
            // 保存最后响应数据用于调试
            self.lastResponseData = data
            
            // 保存会话cookie
            if let httpResponse = response as? HTTPURLResponse {
                if let headerFields = httpResponse.allHeaderFields as? [String: String] {
                    let cookies = HTTPCookie.cookies(withResponseHeaderFields: headerFields, for: url)
                    if !cookies.isEmpty {
                        sessionCookies = cookies
                    }
                    
                    // 检查是否有CSRF令牌在响应头中
                    if let csrfToken = headerFields["X-CSRFToken"] ?? headerFields["x-csrftoken"] {
                        self.csrfToken = csrfToken
                        print("🔑 从响应中获取到CSRF令牌")
                    }
                    
                    // 处理Set-Cookie头，查找CSRF令牌
                    if let setCookie = headerFields["Set-Cookie"] ?? headerFields["set-cookie"] {
                        if let range = setCookie.range(of: "csrftoken=([^;]+)", options: .regularExpression) {
                            let token = String(setCookie[range].dropFirst(10))
                            self.csrfToken = token
                            print("🔑 从Set-Cookie中提取CSRF令牌")
                        }
                    }
                }
                
                // 如果这是CSRF令牌请求，尝试从响应体中获取令牌
                if endpoint.contains("/csrf-token/") {
                    do {
                        if let json = try JSONSerialization.jsonObject(with: data) as? [String: Any],
                           let token = json["csrfToken"] as? String {
                            self.csrfToken = token
                            print("🔑 从CSRF令牌API响应获取到令牌: \(token.prefix(10))...")
                        }
                    } catch {
                        print("⚠️ 无法从CSRF令牌API响应中解析令牌: \(error.localizedDescription)")
                    }
                }
            }
            
            guard let httpResponse = response as? HTTPURLResponse else {
                throw APIError.invalidResponse
            }
            
            #if DEBUG
            print("📥 Response Status: \(httpResponse.statusCode)")
            if let responseString = String(data: data, encoding: .utf8) {
                print("📦 Response Data: \(responseString)")
            }
            #endif
            
            switch httpResponse.statusCode {
            case 200...299:
                do {
                    return try parseJSONData(data)
                } catch {
                    #if DEBUG
                    print("❌ 解析响应数据失败: \(error)")
                    if let responseString = String(data: data, encoding: .utf8) {
                        print("📦 无法解析的JSON数据: \(responseString)")
                    }
                    #endif
                    throw APIError.decodingError(error)
                }
            case 400:
                // 处理验证错误
                do {
                    var errorMessage = "数据验证失败"
                    var errorDetails: [String: [String]]? = nil
                    
                    // 尝试解析详细错误信息
                    if let jsonObject = try JSONSerialization.jsonObject(with: data) as? [String: Any] {
                        if let error = jsonObject["error"] as? String {
                            errorMessage = error
                        } else if let detail = jsonObject["detail"] as? String {
                            errorMessage = detail
                        }
                        
                        // 尝试解析验证详情
                        if let details = jsonObject["details"] as? [String: [String]] {
                            errorDetails = details
                        }
                    }
                    
                    throw APIError.validationError(errorMessage, errorDetails ?? [:])
                } catch {
                    if let apiError = error as? APIError {
                        throw apiError
                    }
                    throw APIError.serverError(httpResponse.statusCode, "数据验证失败")
                }
            case 401:
                // 处理登录接口的特殊情况 - 用户名或密码错误
                if endpoint.contains("/ios/api/auth/login/") {
                    throw APIError.authenticationError("用户名或密码有误，请重新尝试")
                }
                
                // 检查是否是令牌过期
                if isTokenExpired(data) {
                    #if DEBUG
                    print("🔄 令牌过期，准备刷新")
                    #endif
                    
                    // 如果是令牌问题，并且不是刷新令牌本身的请求，尝试刷新令牌
                    if endpoint != "/ios/api/auth/token/refresh/" && self.refreshToken != nil {
                        // 抛出特殊异常，由调用方处理令牌刷新
                        throw APIError.unauthorized
                    }
                }

                throw APIError.unauthorized
            case 429:
                // 处理速率限制错误
                var errorMessage = "请求过于频繁，请稍后再试"
                if let errorData = try? JSONDecoder().decode([String: String].self, from: data) {
                    errorMessage = errorData["message"] ?? errorData["error"] ?? errorData["detail"] ?? errorMessage
                }
                throw APIError.rateLimited(errorMessage)
            default:
                var errorMessage: String?
                if let errorData = try? JSONDecoder().decode([String: String].self, from: data) {
                    errorMessage = errorData["error"] ?? errorData["detail"]
                }
                throw APIError.serverError(httpResponse.statusCode, errorMessage ?? "未知服务器错误")
            }
        } catch {
            // 重点记录网络错误情况
            if let urlError = error as? URLError {
                print("⚠️ URLError: \(urlError.code.rawValue) - \(urlError.localizedDescription)")
                
                // 检查是否为连接问题
                if urlError.code == .notConnectedToInternet ||
                   urlError.code == .networkConnectionLost ||
                   urlError.code == .cannotConnectToHost {
                    print("📱 网络连接问题，请检查网络设置")
                }
                
                // 检查是否为超时
                if urlError.code == .timedOut {
                    print("⏱️ 请求超时，服务器响应时间过长")
                }
                
                throw APIError.networkError(urlError)
            }
            
            // 在发生错误后主动检测网络状态
            NetworkMonitor.shared.performActiveCheck()
            
            // 重新抛出错误
            throw error
        }
    }
    
    // 空响应结构体，用于不需要返回内容的请求
    struct InternalEmptyResponse: Codable {}

    // 修改post方法，确保只有一个CSRF请求
    func post<T: Decodable>(_ endpoint: String, body: [String: Any]) async throws -> T {
        // 对于需要CSRF令牌的请求，先确保有CSRF令牌
        if self.csrfToken == nil && endpoint != "/ios/api/csrf-token/" {
            #if DEBUG
            print("⚠️ POST请求前获取CSRF令牌")
            #endif
            _ = try await fetchCSRFToken()
        }
        
        // 尝试正常请求
        do {
            return try await executeRequest(endpoint: endpoint, method: "POST", body: body)
        } catch APIError.unauthorized {
            // 如果是未授权错误，尝试刷新令牌并重试请求
            if self.refreshToken != nil && endpoint != "/ios/api/auth/token/refresh/" {
                do {
                    await checkTokenValidity()
                    // 使用新令牌重试请求
                    return try await executeRequest(endpoint: endpoint, method: "POST", body: body)
                } catch {
                    // 如果刷新失败，则抛出原始未授权错误
                    throw APIError.unauthorized
                }
            } else {
                // 没有刷新令牌或正在刷新令牌，直接抛出未授权错误
                throw APIError.unauthorized
            }
        } catch let error as APIError where error.userFriendlyMessage.contains("CSRF") {
            // 如果是CSRF错误，尝试重新获取CSRF令牌并重试请求
            print("⚠️ 检测到可能的CSRF错误，尝试刷新CSRF令牌后重试")
            do {
                // 重新获取CSRF令牌
                _ = try await fetchCSRFToken()
                print("✅ 成功刷新CSRF令牌，重试请求")
                // 使用新CSRF令牌重试请求
                return try await executeRequest(endpoint: endpoint, method: "POST", body: body)
            } catch {
                print("❌ 刷新CSRF令牌失败: \(error.localizedDescription)")
                throw error
            }
        }
    }
    
    // 修改put方法，确保只有一个CSRF请求
    func put<T: Decodable>(_ endpoint: String, body: [String: Any]) async throws -> T {
        // 对于需要CSRF令牌的请求，先确保有CSRF令牌
        if self.csrfToken == nil {
            #if DEBUG
            print("⚠️ PUT请求前获取CSRF令牌")
            #endif
            _ = try await fetchCSRFToken()
        }
        
        // 尝试正常请求
        do {
            return try await executeRequest(endpoint: endpoint, method: "PUT", body: body)
        } catch APIError.unauthorized {
            // 如果是未授权错误，尝试刷新令牌并重试请求
            if self.refreshToken != nil {
                do {
                    await checkTokenValidity()
                    // 使用新令牌重试请求
                    return try await executeRequest(endpoint: endpoint, method: "PUT", body: body)
                } catch {
                    // 如果刷新失败，则抛出原始未授权错误
                    throw APIError.unauthorized
                }
            } else {
                // 没有刷新令牌，直接抛出未授权错误
                throw APIError.unauthorized
            }
        } catch let error as APIError where error.userFriendlyMessage.contains("CSRF") {
            // 如果是CSRF错误，尝试重新获取CSRF令牌并重试请求
            print("⚠️ 检测到可能的CSRF错误，尝试刷新CSRF令牌后重试")
            do {
                // 重新获取CSRF令牌
                _ = try await fetchCSRFToken()
                print("✅ 成功刷新CSRF令牌，重试请求")
                // 使用新CSRF令牌重试请求
                return try await executeRequest(endpoint: endpoint, method: "PUT", body: body)
            } catch {
                print("❌ 刷新CSRF令牌失败: \(error.localizedDescription)")
                throw error
            }
        }
    }
    
    func delete<T: Decodable>(_ endpoint: String) async throws -> T {
        // 尝试正常请求
        do {
            // 使用通用DELETE方法，它会处理令牌刷新
            return try await executeRequest(endpoint: endpoint, method: "DELETE")
        } catch APIError.unauthorized {
            // 如果是未授权错误，尝试刷新令牌并重试请求
            if self.refreshToken != nil {
                do {
                    await checkTokenValidity()
                    // 使用新令牌重试请求
                    return try await executeRequest(endpoint: endpoint, method: "DELETE")
                } catch {
                    // 如果刷新失败，则抛出原始未授权错误
                    throw APIError.unauthorized
                }
            } else {
                // 没有刷新令牌，直接抛出未授权错误
                throw APIError.unauthorized
            }
        }
    }
    
    // 获取咖啡豆数据的方法
    func fetchBeans() async {
        do {
            await MainActor.run { self.isLoading = true }
            
            // 使用 APIResponse 包装类型接收数据
            let response: APIResponse<Bean> = try await get("/ios/api/beans/")
            
            await MainActor.run { 
                self.beans = response.results
                self.isLoading = false
                self.error = nil
                
                #if DEBUG
                print("✅ 成功获取 \(response.results.count) 个咖啡豆记录")
                #endif
            }
        } catch let error as APIError {
            await MainActor.run {
                self.error = error
                self.isLoading = false
            }
            
            // 网络错误也需要通知NetworkMonitor
            if case .networkError(let underlyingError) = error {
                #if DEBUG
                print("🔴 fetchBeans中捕获网络错误：\(underlyingError)")
                #endif
                NetworkMonitor.shared.performActiveCheck()
            } else {
                // 其他API错误也可能是网络问题
                #if DEBUG
                print("🔴 fetchBeans中捕获API错误：\(error)")
                #endif
                NetworkMonitor.shared.performActiveCheck()
            }
        } catch {
            await MainActor.run {
                self.error = .unknown
                self.isLoading = false
            }
            
            // 其它错误也尝试通知NetworkMonitor
            #if DEBUG
            print("🔴 fetchBeans中捕获未知错误：\(error)")
            #endif
            NetworkMonitor.shared.performActiveCheck()
        }
    }
    
    // 获取冲煮记录数据的方法
    func fetchBrewLogs() async {
        do {
            await MainActor.run { self.isLoading = true }
            
            // 使用 APIResponse 包装类型接收数据
            let response: APIResponse<BrewLog> = try await get("/ios/api/brewlog/")
            
            await MainActor.run { 
                self.brewLogs = response.results
                self.isLoading = false
                self.error = nil
                
                #if DEBUG
                print("✅ 成功获取 \(response.results.count) 条冲煮记录")
                #endif
            }
        } catch let error as APIError {
            await MainActor.run {
                self.error = error
                self.isLoading = false
            }
            
            // 网络错误也需要通知NetworkMonitor
            if case .networkError(let underlyingError) = error {
                #if DEBUG
                print("🔴 fetchBrewLogs中捕获网络错误：\(underlyingError)")
                #endif
                // 在错误发生后进行主动网络检测
                NetworkMonitor.shared.performActiveCheck()
            } else {
                // 其他API错误也可能是网络问题
                #if DEBUG
                print("🔴 fetchBrewLogs中捕获API错误：\(error)")
                #endif
                NetworkMonitor.shared.performActiveCheck()
            }
        } catch {
            await MainActor.run {
                self.error = .unknown
                self.isLoading = false
            }
            
            // 其它错误也尝试通知NetworkMonitor
            #if DEBUG
            print("🔴 fetchBrewLogs中捕获未知错误：\(error)")
            #endif
            // 在错误发生后进行主动网络检测
            NetworkMonitor.shared.performActiveCheck()
        }
    }
    
    // 删除冲煮记录的方法
    func deleteBrewLog(id: Int) async throws -> Bool {
        do {
            await MainActor.run { self.isLoading = true }
            
            // 使用通用DELETE方法，它会处理令牌刷新
            let _: InternalEmptyResponse = try await delete("/ios/api/brewlog/delete/\(id)/")
            
            await MainActor.run { 
                // 删除成功后，从本地数据中移除该记录
                if let index = self.brewLogs.firstIndex(where: { $0.id == id }) {
                    self.brewLogs.remove(at: index)
                }
                self.isLoading = false
                self.error = nil
            }
            
            return true
            
        } catch let error as APIError {
            await MainActor.run {
                self.error = error
                self.isLoading = false
            }
            throw error
        } catch {
            await MainActor.run {
                self.error = .unknown
                self.isLoading = false
            }
            throw APIError.unknown
        }
    }
    
    // 提供获取baseURL的公开方法
    func getBaseURL() -> String {
        return baseURL
    }
    
    // 提供获取authToken的公开方法
    func getAuthToken() -> String? {
        return authToken
    }
    
    // 执行通用API请求的方法
    func perform<T: Decodable>(_ request: APIRequest) async throws -> T {
        // 构建URL
        var endpoint = request.endpoint
        if !endpoint.hasPrefix("/") {
            endpoint = "/ios/api/\(endpoint)"
        }
        
        // 构建请求URL
        guard let urlComponents = URLComponents(string: baseURL + endpoint) else {
            print("❌ perform方法中无法创建URL组件，基础URL: \(baseURL)，端点: \(endpoint)")
            throw APIError.invalidURL
        }
        
        var components = urlComponents
        
        // 添加URL参数
        if let params = request.parameters, !params.isEmpty {
            var queryItems = params.map { URLQueryItem(name: $0.key, value: $0.value) }
            
            // 如果需要强制刷新，添加force_refresh参数
            if request.forceRefresh {
                queryItems.append(URLQueryItem(name: "force_refresh", value: "true"))
            }
            
            components.queryItems = queryItems
        } else if request.forceRefresh {
            // 如果没有其他参数但需要强制刷新
            components.queryItems = [URLQueryItem(name: "force_refresh", value: "true")]
        }
        
        // 获取完整URL
        guard let finalUrl = components.url?.absoluteString else {
            print("❌ perform方法中无法获取最终URL字符串")
            throw APIError.invalidURL
        }
        
        // 调试输出
        print("🔄 执行API请求: \(finalUrl)")
        
        // 执行请求
        return try await executeRequest(
            endpoint: finalUrl,
            method: request.method.rawValue,
            body: request.body
        )
    }
    
    // 获取所有咖啡豆回购记录
    func fetchBeanOccurrences(beanId: Int) async throws -> [BeanOccurrence] {
        // 这个方法目前没有对应的API端点，当需要时可以在服务端添加
        // 目前可以通过bean_detail接口获取bean的occurrences
        return []
    }
}

// MARK: - Response Types
struct TokenVerifyResponse: Codable {
    let valid: Bool
    let userId: Int
    let username: String
    let lastVerified: String
    
    enum CodingKeys: String, CodingKey {
        case valid
        case userId = "user_id"
        case username
        case lastVerified = "last_verified"
    }
}

// 添加单个咖啡豆的响应结构体
struct SingleBeanResponse: Codable {
    let bean: CoffeeBean
    
    enum CodingKeys: String, CodingKey {
        case bean
    }
}

struct LoginResponse: Codable {
    let id: Int
    let username: String
    let email: String
    let firstName: String?
    let access: String
    let refresh: String
    let expiresIn: Int
    let refreshExpiresIn: Int

    enum CodingKeys: String, CodingKey {
        case id
        case username
        case email
        case firstName = "first_name"
        case access
        case refresh
        case expiresIn = "expires_in"
        case refreshExpiresIn = "refresh_expires_in"
    }
}

struct RegisterResponse: Codable {
    let id: Int
    let username: String
    let email: String
    let firstName: String?
    let access: String
    let refresh: String
    let expiresIn: Int
    let refreshExpiresIn: Int
    let message: String?

    enum CodingKeys: String, CodingKey {
        case id
        case username
        case email
        case firstName = "first_name"
        case access
        case refresh
        case expiresIn = "expires_in"
        case refreshExpiresIn = "refresh_expires_in"
        case message
    }
}

struct PasswordResetResponse: Codable {
    let success: Bool
    let message: String
    let detail: String?
}

struct ProfileUpdateResponse: Codable {
    let success: Bool
    let message: String?
    let firstName: String?
    
    enum CodingKeys: String, CodingKey {
        case success
        case message
        case firstName = "first_name"
    }
}

struct UserDevice: Codable, Identifiable {
    let id: Int
    let deviceId: String
    let hasPushToken: Bool
    let lastLogin: String
    let createdAt: String
    
    enum CodingKeys: String, CodingKey {
        case id
        case deviceId = "device_id"
        case hasPushToken = "has_push_token"
        case lastLogin = "last_login"
        case createdAt = "created_at"
    }
}

struct UserDevicesResponse: Codable {
    let count: Int
    let devices: [UserDevice]
}

// 注：我们刻意避免导入或定义Equipment类型，防止与Models/Equipment.swift发生冲突
// 相关视图模型应直接处理Equipment类型的获取和管理 

// MARK: - 回购记录响应模型
struct CreateOccurrenceResponse: Codable {
    let success: Bool
    let message: String
    let bean: CoffeeBean  // 使用Models.swift中定义的CoffeeBean
    let occurrence: BeanOccurrence  // 使用Models.swift中定义的BeanOccurrence
    let updatedAt: Int
    
    enum CodingKeys: String, CodingKey {
        case success
        case message
        case bean
        case occurrence
        case updatedAt = "updated_at"
    }
}

struct UpdateOccurrenceResponse: Codable {
    let success: Bool
    let message: String
    let bean: CoffeeBean  // 使用Models.swift中定义的CoffeeBean
    let occurrence: BeanOccurrence  // 使用Models.swift中定义的BeanOccurrence
    let updatedAt: Int
    
    enum CodingKeys: String, CodingKey {
        case success
        case message
        case bean
        case occurrence
        case updatedAt = "updated_at"
    }
}

struct DeleteOccurrenceResponse: Codable {
    let success: Bool
    let message: String
    let bean: CoffeeBean  // 使用Models.swift中定义的CoffeeBean
    let updatedAt: Int
    
    enum CodingKeys: String, CodingKey {
        case success
        case message
        case bean
        case updatedAt = "updated_at"
    }
}

// MARK: - JSON 解析处理
// 添加包装器结构体在方法外部
struct ResultsWrapper<WrappedType: Decodable>: Decodable {
    let results: WrappedType
} 